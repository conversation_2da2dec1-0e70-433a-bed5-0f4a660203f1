/**
 * Application menu management module
 */

const { Menu, dialog, app } = require('electron');

class MenuManager {
  constructor(windowManager) {
    this.windowManager = windowManager;
  }

  /**
   * Create and set the application menu
   */
  createMenu() {
    const template = this.getMenuTemplate();
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  /**
   * Get the menu template
   * @returns {Array} Menu template
   */
  getMenuTemplate() {
    return [
      this.getFileMenu(),
      this.getViewMenu(),
      this.getToolsMenu(),
      this.getWindowMenu(),
      this.getHelpMenu()
    ];
  }

  /**
   * Get File menu
   * @returns {Object} File menu template
   */
  getFileMenu() {
    return {
      label: 'File',
      submenu: [
        {
          label: 'Add Website',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            this.windowManager.sendToMainWindow('show-add-website-dialog');
          }
        },
        {
          label: 'Import Websites',
          click: async () => {
            const result = await dialog.showOpenDialog(this.windowManager.getMainWindow(), {
              properties: ['openFile'],
              filters: [
                { name: 'JSON Files', extensions: ['json'] }
              ]
            });
            
            if (!result.canceled) {
              this.windowManager.sendToMainWindow('import-websites', result.filePaths[0]);
            }
          }
        },
        {
          label: 'Export Websites',
          click: async () => {
            const result = await dialog.showSaveDialog(this.windowManager.getMainWindow(), {
              filters: [
                { name: 'JSON Files', extensions: ['json'] }
              ]
            });
            
            if (!result.canceled) {
              this.windowManager.sendToMainWindow('export-websites', result.filePath);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    };
  }

  /**
   * Get View menu
   * @returns {Object} View menu template
   */
  getViewMenu() {
    return {
      label: 'View',
      submenu: [
        {
          label: 'Reload Current Site',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            this.windowManager.sendToMainWindow('reload-current-site');
          }
        },
        {
          label: 'Toggle DevTools',
          accelerator: 'F12',
          click: () => {
            this.windowManager.toggleDevTools();
          }
        },
        { type: 'separator' },
        {
          label: 'Zoom In',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => {
            this.windowManager.sendToMainWindow('zoom-in');
          }
        },
        {
          label: 'Zoom Out',
          accelerator: 'CmdOrCtrl+-',
          click: () => {
            this.windowManager.sendToMainWindow('zoom-out');
          }
        },
        {
          label: 'Reset Zoom',
          accelerator: 'CmdOrCtrl+0',
          click: () => {
            this.windowManager.sendToMainWindow('reset-zoom');
          }
        },
        { type: 'separator' },
        {
          label: 'Toggle Fullscreen',
          accelerator: 'F11',
          click: () => {
            const mainWindow = this.windowManager.getMainWindow();
            if (mainWindow) {
              mainWindow.setFullScreen(!mainWindow.isFullScreen());
            }
          }
        }
      ]
    };
  }

  /**
   * Get Tools menu
   * @returns {Object} Tools menu template
   */
  getToolsMenu() {
    return {
      label: 'Tools',
      submenu: [
        {
          label: 'History',
          accelerator: 'CmdOrCtrl+H',
          click: () => {
            this.windowManager.sendToMainWindow('show-history');
          }
        },
        {
          label: 'Downloads',
          accelerator: 'CmdOrCtrl+J',
          click: () => {
            this.windowManager.sendToMainWindow('show-downloads');
          }
        },
        {
          label: 'Find in Page',
          accelerator: 'CmdOrCtrl+F',
          click: () => {
            this.windowManager.sendToMainWindow('show-find');
          }
        },
        { type: 'separator' },
        {
          label: 'Developer Tools',
          accelerator: 'CmdOrCtrl+Shift+I',
          click: () => {
            this.windowManager.sendToMainWindow('toggle-dev-tools');
          }
        },
        { type: 'separator' },
        {
          label: 'Clear All Data',
          click: () => {
            this.windowManager.sendToMainWindow('clear-all-data');
          }
        },
        {
          label: 'Settings',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            this.windowManager.sendToMainWindow('show-settings');
          }
        }
      ]
    };
  }

  /**
   * Get Window menu
   * @returns {Object} Window menu template
   */
  getWindowMenu() {
    return {
      label: 'Window',
      submenu: [
        {
          label: 'New Tab',
          accelerator: 'CmdOrCtrl+T',
          click: () => {
            this.windowManager.sendToMainWindow('new-tab');
          }
        },
        {
          label: 'Close Tab',
          accelerator: 'CmdOrCtrl+W',
          click: () => {
            this.windowManager.sendToMainWindow('close-tab');
          }
        },
        {
          label: 'Next Tab',
          accelerator: 'CmdOrCtrl+Tab',
          click: () => {
            this.windowManager.sendToMainWindow('next-tab');
          }
        },
        {
          label: 'Previous Tab',
          accelerator: 'CmdOrCtrl+Shift+Tab',
          click: () => {
            this.windowManager.sendToMainWindow('prev-tab');
          }
        },
        { type: 'separator' },
        {
          label: 'Minimize',
          accelerator: 'CmdOrCtrl+M',
          role: 'minimize'
        },
        {
          label: 'Close',
          accelerator: 'CmdOrCtrl+Shift+W',
          role: 'close'
        }
      ]
    };
  }

  /**
   * Get Help menu
   * @returns {Object} Help menu template
   */
  getHelpMenu() {
    return {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            this.windowManager.sendToMainWindow('show-about');
          }
        },
        {
          label: 'Keyboard Shortcuts',
          accelerator: 'CmdOrCtrl+?',
          click: () => {
            this.windowManager.sendToMainWindow('show-shortcuts');
          }
        },
        { type: 'separator' },
        {
          label: 'Report Issue',
          click: () => {
            // Open issue reporting URL
            require('electron').shell.openExternal('https://github.com/your-repo/issues');
          }
        }
      ]
    };
  }

  /**
   * Update menu state based on application state
   * @param {Object} state - Application state
   */
  updateMenuState(state) {
    // This method can be used to enable/disable menu items based on app state
    // For example, disable "Close Tab" when no tabs are open
    const menu = Menu.getApplicationMenu();
    if (!menu) return;

    // Example: Update menu items based on state
    if (state.hasActiveTabs !== undefined) {
      const closeTabItem = menu.getMenuItemById('close-tab');
      if (closeTabItem) {
        closeTabItem.enabled = state.hasActiveTabs;
      }
    }
  }

  /**
   * Get context menu for different elements
   * @param {string} type - Context menu type
   * @param {Object} data - Context data
   * @returns {Menu} Context menu
   */
  getContextMenu(type, data = {}) {
    let template = [];

    switch (type) {
      case 'website':
        template = this.getWebsiteContextMenu(data);
        break;
      case 'tab':
        template = this.getTabContextMenu(data);
        break;
      case 'webview':
        template = this.getWebviewContextMenu(data);
        break;
      default:
        template = [];
    }

    return Menu.buildFromTemplate(template);
  }

  /**
   * Get website context menu template
   * @param {Object} data - Website data
   * @returns {Array} Context menu template
   */
  getWebsiteContextMenu(data) {
    return [
      {
        label: 'Open',
        click: () => {
          this.windowManager.sendToMainWindow('open-website', data.id);
        }
      },
      {
        label: 'Open in New Tab',
        click: () => {
          this.windowManager.sendToMainWindow('open-website-new-tab', data.id);
        }
      },
      { type: 'separator' },
      {
        label: 'Edit',
        click: () => {
          this.windowManager.sendToMainWindow('edit-website', data.id);
        }
      },
      {
        label: data.isFavorite ? 'Remove from Favorites' : 'Add to Favorites',
        click: () => {
          this.windowManager.sendToMainWindow('toggle-website-favorite', data.id);
        }
      },
      { type: 'separator' },
      {
        label: 'Copy URL',
        click: () => {
          require('electron').clipboard.writeText(data.url);
        }
      },
      { type: 'separator' },
      {
        label: 'Delete',
        click: () => {
          this.windowManager.sendToMainWindow('delete-website', data.id);
        }
      }
    ];
  }

  /**
   * Get tab context menu template
   * @param {Object} data - Tab data
   * @returns {Array} Context menu template
   */
  getTabContextMenu(data) {
    return [
      {
        label: 'Reload',
        click: () => {
          this.windowManager.sendToMainWindow('reload-tab', data.id);
        }
      },
      {
        label: 'Duplicate',
        click: () => {
          this.windowManager.sendToMainWindow('duplicate-tab', data.id);
        }
      },
      { type: 'separator' },
      {
        label: 'Close',
        click: () => {
          this.windowManager.sendToMainWindow('close-tab', data.id);
        }
      },
      {
        label: 'Close Other Tabs',
        click: () => {
          this.windowManager.sendToMainWindow('close-other-tabs', data.id);
        }
      }
    ];
  }

  /**
   * Get webview context menu template
   * @param {Object} data - Webview data
   * @returns {Array} Context menu template
   */
  getWebviewContextMenu(data) {
    return [
      {
        label: 'Back',
        enabled: data.canGoBack,
        click: () => {
          this.windowManager.sendToMainWindow('webview-go-back', data.tabId);
        }
      },
      {
        label: 'Forward',
        enabled: data.canGoForward,
        click: () => {
          this.windowManager.sendToMainWindow('webview-go-forward', data.tabId);
        }
      },
      {
        label: 'Reload',
        click: () => {
          this.windowManager.sendToMainWindow('webview-reload', data.tabId);
        }
      },
      { type: 'separator' },
      {
        label: 'View Page Source',
        click: () => {
          this.windowManager.sendToMainWindow('webview-view-source', data.tabId);
        }
      },
      {
        label: 'Inspect Element',
        click: () => {
          this.windowManager.sendToMainWindow('webview-inspect', data.tabId);
        }
      }
    ];
  }
}

module.exports = MenuManager;
