/**
 * Zoom controller component for managing page zoom levels
 */

import { ZOOM_LEVELS } from '../../shared/constants.js';

export class ZoomController {
  constructor(eventService, webviewManager, dataService = null) {
    this.eventService = eventService;
    this.webviewManager = webviewManager;
    this.dataService = dataService;
    
    // Get DOM elements
    this.zoomInBtn = document.getElementById('zoomInBtn');
    this.zoomOutBtn = document.getElementById('zoomOutBtn');
    this.zoomLevel = document.getElementById('zoomLevel');
    
    this.currentZoom = 1.0;
    this.zoomLevels = ZOOM_LEVELS;
    this.defaultZoom = 1.0;
    
    this.setupEventListeners();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Zoom buttons
    if (this.zoomInBtn) {
      this.zoomInBtn.addEventListener('click', () => {
        this.zoomIn();
      });
    }

    if (this.zoomOutBtn) {
      this.zoomOutBtn.addEventListener('click', () => {
        this.zoomOut();
      });
    }

    // Zoom level display click to reset
    if (this.zoomLevel) {
      this.zoomLevel.addEventListener('click', () => {
        this.resetZoom();
      });
      this.zoomLevel.style.cursor = 'pointer';
      this.zoomLevel.title = 'Click to reset zoom';
    }

    // Event service listeners
    this.eventService.on('zoom-in', () => {
      this.zoomIn();
    });

    this.eventService.on('zoom-out', () => {
      this.zoomOut();
    });

    this.eventService.on('reset-zoom', () => {
      this.resetZoom();
    });

    this.eventService.on('zoom-changed', (zoomFactor) => {
      this.updateZoomDisplay(zoomFactor);
    });

    this.eventService.on('tab-switched', (tab) => {
      this.updateFromTab(tab);
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '+':
          case '=':
            e.preventDefault();
            this.zoomIn();
            break;
          case '-':
            e.preventDefault();
            this.zoomOut();
            break;
          case '0':
            e.preventDefault();
            this.resetZoom();
            break;
        }
      }
    });

    // Mouse wheel zoom (with Ctrl)
    document.addEventListener('wheel', (e) => {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        if (e.deltaY < 0) {
          this.zoomIn();
        } else {
          this.zoomOut();
        }
      }
    }, { passive: false });
  }

  /**
   * Zoom in
   */
  zoomIn() {
    const webview = this.webviewManager.getActiveWebview();
    if (!webview) return;

    const currentZoom = webview.getZoomFactor();
    const newZoom = this.getNextZoomLevel(currentZoom, 'in');
    
    if (newZoom !== currentZoom) {
      webview.setZoomFactor(newZoom);
      this.updateZoomDisplay(newZoom);
      this.updateTabZoom(newZoom);
    }
  }

  /**
   * Zoom out
   */
  zoomOut() {
    const webview = this.webviewManager.getActiveWebview();
    if (!webview) return;

    const currentZoom = webview.getZoomFactor();
    const newZoom = this.getNextZoomLevel(currentZoom, 'out');
    
    if (newZoom !== currentZoom) {
      webview.setZoomFactor(newZoom);
      this.updateZoomDisplay(newZoom);
      this.updateTabZoom(newZoom);
    }
  }

  /**
   * Reset zoom to default
   */
  resetZoom() {
    const webview = this.webviewManager.getActiveWebview();
    if (!webview) return;

    webview.setZoomFactor(this.defaultZoom);
    this.updateZoomDisplay(this.defaultZoom);
    this.updateTabZoom(this.defaultZoom);
  }

  /**
   * Set specific zoom level
   * @param {number} zoomFactor - Zoom factor (0.25 to 5.0)
   */
  setZoom(zoomFactor) {
    const webview = this.webviewManager.getActiveWebview();
    if (!webview) return;

    const clampedZoom = Math.max(0.25, Math.min(5.0, zoomFactor));
    webview.setZoomFactor(clampedZoom);
    this.updateZoomDisplay(clampedZoom);
    this.updateTabZoom(clampedZoom);
  }

  /**
   * Get next zoom level
   * @param {number} currentZoom - Current zoom factor
   * @param {string} direction - 'in' or 'out'
   * @returns {number} Next zoom level
   */
  getNextZoomLevel(currentZoom, direction) {
    const currentIndex = this.findClosestZoomIndex(currentZoom);
    
    if (direction === 'in') {
      return currentIndex < this.zoomLevels.length - 1 
        ? this.zoomLevels[currentIndex + 1] 
        : currentZoom;
    } else {
      return currentIndex > 0 
        ? this.zoomLevels[currentIndex - 1] 
        : currentZoom;
    }
  }

  /**
   * Find closest zoom level index
   * @param {number} zoomFactor - Zoom factor
   * @returns {number} Closest index
   */
  findClosestZoomIndex(zoomFactor) {
    let closestIndex = 0;
    let closestDiff = Math.abs(this.zoomLevels[0] - zoomFactor);

    for (let i = 1; i < this.zoomLevels.length; i++) {
      const diff = Math.abs(this.zoomLevels[i] - zoomFactor);
      if (diff < closestDiff) {
        closestDiff = diff;
        closestIndex = i;
      }
    }

    return closestIndex;
  }

  /**
   * Update zoom display
   * @param {number} zoomFactor - Current zoom factor
   */
  updateZoomDisplay(zoomFactor) {
    this.currentZoom = zoomFactor;
    
    if (this.zoomLevel) {
      const percentage = Math.round(zoomFactor * 100);
      this.zoomLevel.textContent = `${percentage}%`;
      
      // Update button states
      this.updateButtonStates(zoomFactor);
    }
  }

  /**
   * Update button states
   * @param {number} zoomFactor - Current zoom factor
   */
  updateButtonStates(zoomFactor) {
    const minZoom = this.zoomLevels[0];
    const maxZoom = this.zoomLevels[this.zoomLevels.length - 1];
    
    if (this.zoomOutBtn) {
      this.zoomOutBtn.disabled = zoomFactor <= minZoom;
    }
    
    if (this.zoomInBtn) {
      this.zoomInBtn.disabled = zoomFactor >= maxZoom;
    }
  }

  /**
   * Update tab zoom level
   * @param {number} zoomFactor - Zoom factor
   */
  updateTabZoom(zoomFactor) {
    const activeTab = this.dataService?.getActiveTab();
    if (activeTab) {
      activeTab.setZoomLevel(zoomFactor);
    }
  }

  /**
   * Update from tab data
   * @param {Tab} tab - Tab data
   */
  updateFromTab(tab) {
    if (tab && tab.zoomLevel) {
      this.updateZoomDisplay(tab.zoomLevel);
      
      // Apply zoom to webview if it exists
      const webview = this.webviewManager.getActiveWebview();
      if (webview) {
        webview.setZoomFactor(tab.zoomLevel);
      }
    }
  }

  /**
   * Get current zoom factor
   * @returns {number} Current zoom factor
   */
  getCurrentZoom() {
    const webview = this.webviewManager.getActiveWebview();
    return webview ? webview.getZoomFactor() : this.defaultZoom;
  }

  /**
   * Get zoom percentage
   * @returns {number} Zoom percentage
   */
  getZoomPercentage() {
    return Math.round(this.getCurrentZoom() * 100);
  }

  /**
   * Set zoom from percentage
   * @param {number} percentage - Zoom percentage (25-500)
   */
  setZoomFromPercentage(percentage) {
    const zoomFactor = percentage / 100;
    this.setZoom(zoomFactor);
  }

  /**
   * Create zoom menu
   * @returns {Array} Zoom menu items
   */
  createZoomMenu() {
    return this.zoomLevels.map(level => ({
      label: `${Math.round(level * 100)}%`,
      value: level,
      active: Math.abs(level - this.currentZoom) < 0.01,
      click: () => this.setZoom(level)
    }));
  }

  /**
   * Show zoom menu
   * @param {Event} event - Click event
   */
  showZoomMenu(event) {
    const menu = this.createZoomMenu();
    this.eventService.emit('show-context-menu', {
      event,
      items: menu
    });
  }

  /**
   * Get zoom info
   * @returns {Object} Zoom information
   */
  getZoomInfo() {
    const currentZoom = this.getCurrentZoom();
    return {
      factor: currentZoom,
      percentage: Math.round(currentZoom * 100),
      canZoomIn: currentZoom < this.zoomLevels[this.zoomLevels.length - 1],
      canZoomOut: currentZoom > this.zoomLevels[0],
      isDefault: Math.abs(currentZoom - this.defaultZoom) < 0.01
    };
  }

  /**
   * Save zoom preferences
   */
  saveZoomPreferences() {
    const zoomInfo = this.getZoomInfo();
    this.eventService.emit('save-zoom-preferences', zoomInfo);
  }

  /**
   * Load zoom preferences
   * @param {Object} preferences - Zoom preferences
   */
  loadZoomPreferences(preferences) {
    if (preferences && preferences.defaultZoom) {
      this.defaultZoom = preferences.defaultZoom;
    }
  }

  /**
   * Reset all zoom levels
   */
  resetAllZoom() {
    // Reset all webviews to default zoom
    this.webviewManager.webviews.forEach(webview => {
      webview.setZoomFactor(this.defaultZoom);
    });
    
    this.updateZoomDisplay(this.defaultZoom);
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.currentZoom = this.defaultZoom;
    this.updateZoomDisplay(this.defaultZoom);
  }
}
