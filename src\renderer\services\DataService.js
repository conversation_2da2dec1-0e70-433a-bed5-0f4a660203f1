/**
 * Data service for managing application data
 */

import { Website } from '../models/Website.js';
import { Tab } from '../models/Tab.js';
import { HistoryEntry } from '../models/HistoryEntry.js';
import { Download } from '../models/Download.js';
import { DEFAULT_SETTINGS } from '../../shared/constants.js';
import { logError, logInfo } from '../../shared/utils.js';

export class DataService {
  constructor(storageService, eventService) {
    this.storageService = storageService;
    this.eventService = eventService;
    this.websites = [];
    this.tabs = [];
    this.history = [];
    this.downloads = [];
    this.settings = { ...DEFAULT_SETTINGS };
    this.isLoaded = false;
  }

  /**
   * Initialize and load all data
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logInfo('DataService', 'Loading application data...');
      
      await this.loadWebsites();
      await this.loadSettings();
      await this.loadHistory();
      await this.loadDownloads();
      
      this.isLoaded = true;
      this.eventService.emit('data-loaded');
      
      logInfo('DataService', 'Application data loaded successfully');
    } catch (error) {
      logError('DataService', `Error loading data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load websites from storage
   * @returns {Promise<void>}
   */
  async loadWebsites() {
    try {
      const websiteData = await this.storageService.getWebsites();
      this.websites = websiteData.map(data => Website.fromJSON(data));
      logInfo('DataService', `Loaded ${this.websites.length} websites`);
    } catch (error) {
      logError('DataService', `Error loading websites: ${error.message}`);
      this.websites = [];
    }
  }

  /**
   * Save websites to storage
   * @returns {Promise<void>}
   */
  async saveWebsites() {
    try {
      const websiteData = this.websites.map(website => website.toJSON());
      await this.storageService.saveWebsites(websiteData);
      this.eventService.emit('websites-saved');
    } catch (error) {
      logError('DataService', `Error saving websites: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load settings from storage
   * @returns {Promise<void>}
   */
  async loadSettings() {
    try {
      const settingsData = await this.storageService.getSettings();
      this.settings = { ...DEFAULT_SETTINGS, ...settingsData };
      logInfo('DataService', 'Settings loaded successfully');
    } catch (error) {
      logError('DataService', `Error loading settings: ${error.message}`);
      this.settings = { ...DEFAULT_SETTINGS };
    }
  }

  /**
   * Save settings to storage
   * @returns {Promise<void>}
   */
  async saveSettings() {
    try {
      await this.storageService.saveSettings(this.settings);
      this.eventService.emit('settings-saved');
    } catch (error) {
      logError('DataService', `Error saving settings: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load history from storage
   * @returns {Promise<void>}
   */
  async loadHistory() {
    try {
      const historyData = await this.storageService.getHistory();
      this.history = historyData.map(data => HistoryEntry.fromJSON(data));
      logInfo('DataService', `Loaded ${this.history.length} history entries`);
    } catch (error) {
      logError('DataService', `Error loading history: ${error.message}`);
      this.history = [];
    }
  }

  /**
   * Save history to storage
   * @returns {Promise<void>}
   */
  async saveHistory() {
    try {
      const historyData = this.history.map(entry => entry.toJSON());
      await this.storageService.saveHistory(historyData);
      this.eventService.emit('history-saved');
    } catch (error) {
      logError('DataService', `Error saving history: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load downloads from storage
   * @returns {Promise<void>}
   */
  async loadDownloads() {
    try {
      const downloadData = await this.storageService.getDownloads();
      this.downloads = downloadData.map(data => Download.fromJSON(data));
      logInfo('DataService', `Loaded ${this.downloads.length} downloads`);
    } catch (error) {
      logError('DataService', `Error loading downloads: ${error.message}`);
      this.downloads = [];
    }
  }

  /**
   * Save downloads to storage
   * @returns {Promise<void>}
   */
  async saveDownloads() {
    try {
      const downloadData = this.downloads.map(download => download.toJSON());
      await this.storageService.saveDownloads(downloadData);
      this.eventService.emit('downloads-saved');
    } catch (error) {
      logError('DataService', `Error saving downloads: ${error.message}`);
      throw error;
    }
  }

  // Website management methods
  
  /**
   * Add a new website
   * @param {Object} websiteData - Website data
   * @returns {Website} Created website
   */
  addWebsite(websiteData) {
    const website = new Website(websiteData);
    this.websites.push(website);
    this.saveWebsites();
    this.eventService.emit('website-added', website);
    return website;
  }

  /**
   * Update a website
   * @param {string} websiteId - Website ID
   * @param {Object} updateData - Update data
   * @returns {Website|null} Updated website
   */
  updateWebsite(websiteId, updateData) {
    const website = this.getWebsiteById(websiteId);
    if (website) {
      website.update(updateData);
      this.saveWebsites();
      this.eventService.emit('website-updated', website);
      return website;
    }
    return null;
  }

  /**
   * Delete a website
   * @param {string} websiteId - Website ID
   * @returns {boolean} True if deleted
   */
  deleteWebsite(websiteId) {
    const index = this.websites.findIndex(w => w.id === websiteId);
    if (index !== -1) {
      const website = this.websites[index];
      this.websites.splice(index, 1);
      this.saveWebsites();
      this.eventService.emit('website-deleted', website);
      return true;
    }
    return false;
  }

  /**
   * Get website by ID
   * @param {string} websiteId - Website ID
   * @returns {Website|null} Website or null
   */
  getWebsiteById(websiteId) {
    return this.websites.find(w => w.id === websiteId) || null;
  }

  /**
   * Search websites
   * @param {string} query - Search query
   * @returns {Website[]} Matching websites
   */
  searchWebsites(query) {
    if (!query || query.trim().length === 0) {
      return this.websites;
    }
    return this.websites.filter(website => website.matchesSearch(query));
  }

  /**
   * Get websites by category
   * @param {string} category - Category name
   * @returns {Website[]} Websites in category
   */
  getWebsitesByCategory(category) {
    return this.websites.filter(w => w.category === category);
  }

  /**
   * Get favorite websites
   * @returns {Website[]} Favorite websites
   */
  getFavoriteWebsites() {
    return this.websites.filter(w => w.isFavorite);
  }

  // Tab management methods

  /**
   * Add a new tab
   * @param {Object} tabData - Tab data
   * @returns {Tab} Created tab
   */
  addTab(tabData) {
    const tab = new Tab(tabData);
    this.tabs.push(tab);
    this.eventService.emit('tab-added', tab);
    return tab;
  }

  /**
   * Remove a tab
   * @param {string} tabId - Tab ID
   * @returns {boolean} True if removed
   */
  removeTab(tabId) {
    const index = this.tabs.findIndex(t => t.id === tabId);
    if (index !== -1) {
      const tab = this.tabs[index];
      this.tabs.splice(index, 1);
      this.eventService.emit('tab-removed', tab);
      return true;
    }
    return false;
  }

  /**
   * Get tab by ID
   * @param {string} tabId - Tab ID
   * @returns {Tab|null} Tab or null
   */
  getTabById(tabId) {
    return this.tabs.find(t => t.id === tabId) || null;
  }

  /**
   * Get active tab
   * @returns {Tab|null} Active tab or null
   */
  getActiveTab() {
    return this.tabs.find(t => t.isActive) || null;
  }

  /**
   * Set active tab
   * @param {string} tabId - Tab ID
   */
  setActiveTab(tabId) {
    this.tabs.forEach(tab => {
      tab.setActive(tab.id === tabId);
    });
    this.eventService.emit('active-tab-changed', tabId);
  }

  // History management methods

  /**
   * Add history entry
   * @param {Object} entryData - History entry data
   * @returns {HistoryEntry} Created entry
   */
  addHistoryEntry(entryData) {
    // Check if entry already exists for this URL
    const existingEntry = this.history.find(h => h.url === entryData.url);
    
    if (existingEntry) {
      existingEntry.recordVisit();
      if (entryData.title) {
        existingEntry.updateMetadata(entryData.title, entryData.favicon);
      }
      this.saveHistory();
      this.eventService.emit('history-updated', existingEntry);
      return existingEntry;
    } else {
      const entry = new HistoryEntry(entryData);
      this.history.unshift(entry); // Add to beginning
      
      // Limit history size
      if (this.history.length > 10000) {
        this.history = this.history.slice(0, 10000);
      }
      
      this.saveHistory();
      this.eventService.emit('history-added', entry);
      return entry;
    }
  }

  /**
   * Clear history
   */
  clearHistory() {
    this.history = [];
    this.saveHistory();
    this.eventService.emit('history-cleared');
  }

  /**
   * Search history
   * @param {string} query - Search query
   * @returns {HistoryEntry[]} Matching entries
   */
  searchHistory(query) {
    if (!query || query.trim().length === 0) {
      return this.history;
    }
    return this.history.filter(entry => entry.matchesSearch(query));
  }

  // Download management methods

  /**
   * Add download
   * @param {Object} downloadData - Download data
   * @returns {Download} Created download
   */
  addDownload(downloadData) {
    const download = new Download(downloadData);
    this.downloads.unshift(download); // Add to beginning
    this.saveDownloads();
    this.eventService.emit('download-added', download);
    return download;
  }

  /**
   * Update download
   * @param {string} downloadId - Download ID
   * @param {Object} updateData - Update data
   * @returns {Download|null} Updated download
   */
  updateDownload(downloadId, updateData) {
    const download = this.downloads.find(d => d.id === downloadId);
    if (download) {
      Object.assign(download, updateData);
      this.saveDownloads();
      this.eventService.emit('download-updated', download);
      return download;
    }
    return null;
  }

  /**
   * Clear downloads
   */
  clearDownloads() {
    this.downloads = [];
    this.saveDownloads();
    this.eventService.emit('downloads-cleared');
  }

  /**
   * Export all data
   * @returns {Promise<Object>} Exported data
   */
  async exportData() {
    return await this.storageService.exportData();
  }

  /**
   * Import data
   * @param {Object} data - Data to import
   * @returns {Promise<void>}
   */
  async importData(data) {
    await this.storageService.importData(data);
    await this.initialize(); // Reload all data
    this.eventService.emit('data-imported');
  }

  /**
   * Clear all data
   * @returns {Promise<void>}
   */
  async clearAllData() {
    this.websites = [];
    this.tabs = [];
    this.history = [];
    this.downloads = [];
    this.settings = { ...DEFAULT_SETTINGS };
    
    await this.storageService.clear();
    this.eventService.emit('all-data-cleared');
  }
}
