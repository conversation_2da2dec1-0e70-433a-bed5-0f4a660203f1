/**
 * Storage service for renderer process
 */

import { STORAGE_KEYS, ERROR_MESSAGES } from '../../shared/constants.js';
import { logError, logInfo } from '../../shared/utils.js';

export class StorageService {
  constructor() {
    this.electronAPI = window.electronAPI;
  }

  /**
   * Get value from storage
   * @param {string} key - Storage key
   * @returns {Promise<any>} Stored value
   */
  async get(key) {
    try {
      const value = await this.electronAPI.storeGet(key);
      logInfo('StorageService', `Retrieved key: ${key}`);
      return value;
    } catch (error) {
      logError('StorageService', `Error getting key ${key}: ${error.message}`);
      throw new Error(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }

  /**
   * Set value in storage
   * @param {string} key - Storage key
   * @param {any} value - Value to store
   * @returns {Promise<void>}
   */
  async set(key, value) {
    try {
      await this.electronAPI.storeSet(key, value);
      logInfo('StorageService', `Stored key: ${key}`);
    } catch (error) {
      logError('StorageService', `Error setting key ${key}: ${error.message}`);
      throw new Error(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }

  /**
   * Delete key from storage
   * @param {string} key - Storage key
   * @returns {Promise<void>}
   */
  async delete(key) {
    try {
      await this.electronAPI.storeDelete(key);
      logInfo('StorageService', `Deleted key: ${key}`);
    } catch (error) {
      logError('StorageService', `Error deleting key ${key}: ${error.message}`);
      throw new Error(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }

  /**
   * Clear all storage
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      await this.electronAPI.storeClear();
      logInfo('StorageService', 'Cleared all storage');
    } catch (error) {
      logError('StorageService', `Error clearing storage: ${error.message}`);
      throw new Error(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }

  /**
   * Get websites from storage
   * @returns {Promise<Array>} Array of websites
   */
  async getWebsites() {
    const websites = await this.get(STORAGE_KEYS.WEBSITES);
    return websites || [];
  }

  /**
   * Save websites to storage
   * @param {Array} websites - Array of websites
   * @returns {Promise<void>}
   */
  async saveWebsites(websites) {
    await this.set(STORAGE_KEYS.WEBSITES, websites);
  }

  /**
   * Get settings from storage
   * @returns {Promise<Object>} Settings object
   */
  async getSettings() {
    const settings = await this.get(STORAGE_KEYS.SETTINGS);
    return settings || {};
  }

  /**
   * Save settings to storage
   * @param {Object} settings - Settings object
   * @returns {Promise<void>}
   */
  async saveSettings(settings) {
    await this.set(STORAGE_KEYS.SETTINGS, settings);
  }

  /**
   * Get history from storage
   * @returns {Promise<Array>} Array of history entries
   */
  async getHistory() {
    const history = await this.get(STORAGE_KEYS.HISTORY);
    return history || [];
  }

  /**
   * Save history to storage
   * @param {Array} history - Array of history entries
   * @returns {Promise<void>}
   */
  async saveHistory(history) {
    await this.set(STORAGE_KEYS.HISTORY, history);
  }

  /**
   * Get downloads from storage
   * @returns {Promise<Array>} Array of downloads
   */
  async getDownloads() {
    const downloads = await this.get(STORAGE_KEYS.DOWNLOADS);
    return downloads || [];
  }

  /**
   * Save downloads to storage
   * @param {Array} downloads - Array of downloads
   * @returns {Promise<void>}
   */
  async saveDownloads(downloads) {
    await this.set(STORAGE_KEYS.DOWNLOADS, downloads);
  }

  /**
   * Get last website from storage
   * @returns {Promise<string|null>} Last website ID
   */
  async getLastWebsite() {
    return await this.get(STORAGE_KEYS.LAST_WEBSITE);
  }

  /**
   * Save last website to storage
   * @param {string} websiteId - Website ID
   * @returns {Promise<void>}
   */
  async saveLastWebsite(websiteId) {
    await this.set(STORAGE_KEYS.LAST_WEBSITE, websiteId);
  }

  /**
   * Get window state from storage
   * @returns {Promise<Object|null>} Window state
   */
  async getWindowState() {
    return await this.get(STORAGE_KEYS.WINDOW_STATE);
  }

  /**
   * Save window state to storage
   * @param {Object} state - Window state
   * @returns {Promise<void>}
   */
  async saveWindowState(state) {
    await this.set(STORAGE_KEYS.WINDOW_STATE, state);
  }

  /**
   * Export all data
   * @returns {Promise<Object>} All stored data
   */
  async exportData() {
    try {
      const data = {
        websites: await this.getWebsites(),
        settings: await this.getSettings(),
        history: await this.getHistory(),
        downloads: await this.getDownloads(),
        lastWebsite: await this.getLastWebsite(),
        windowState: await this.getWindowState(),
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      };
      
      logInfo('StorageService', 'Data exported successfully');
      return data;
    } catch (error) {
      logError('StorageService', `Error exporting data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Import data
   * @param {Object} data - Data to import
   * @returns {Promise<void>}
   */
  async importData(data) {
    try {
      if (data.websites) {
        await this.saveWebsites(data.websites);
      }
      
      if (data.settings) {
        await this.saveSettings(data.settings);
      }
      
      if (data.history) {
        await this.saveHistory(data.history);
      }
      
      if (data.downloads) {
        await this.saveDownloads(data.downloads);
      }
      
      if (data.lastWebsite) {
        await this.saveLastWebsite(data.lastWebsite);
      }
      
      if (data.windowState) {
        await this.saveWindowState(data.windowState);
      }
      
      logInfo('StorageService', 'Data imported successfully');
    } catch (error) {
      logError('StorageService', `Error importing data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Backup data to file
   * @param {string} filePath - File path to save backup
   * @returns {Promise<void>}
   */
  async backupToFile(filePath) {
    try {
      const data = await this.exportData();
      const content = JSON.stringify(data, null, 2);
      await this.electronAPI.writeFile(filePath, content);
      logInfo('StorageService', `Data backed up to: ${filePath}`);
    } catch (error) {
      logError('StorageService', `Error backing up data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Restore data from file
   * @param {string} filePath - File path to restore from
   * @returns {Promise<void>}
   */
  async restoreFromFile(filePath) {
    try {
      const content = await this.electronAPI.readFile(filePath);
      const data = JSON.parse(content);
      await this.importData(data);
      logInfo('StorageService', `Data restored from: ${filePath}`);
    } catch (error) {
      logError('StorageService', `Error restoring data: ${error.message}`);
      throw error;
    }
  }
}
