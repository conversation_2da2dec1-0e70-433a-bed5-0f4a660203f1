/**
 * Renderer process entry point - Refactored modular architecture
 */

// Import services
import { StorageService } from './services/StorageService.js';
import { EventService } from './services/EventService.js';
import { DataService } from './services/DataService.js';

// Import components
import { TabManager } from './components/TabManager.js';
import { WebsiteManager } from './components/WebsiteManager.js';

// Import utilities
import { logInfo, logError } from '../shared/utils.js';
import { DEFAULT_SETTINGS } from '../shared/constants.js';

class UnrestrictedBrowserRenderer {
  constructor() {
    this.services = {};
    this.components = {};
    this.isInitialized = false;
  }

  /**
   * Initialize the renderer application
   */
  async initialize() {
    try {
      logInfo('Renderer', 'Initializing Unrestricted Browser renderer...');

      // Initialize services
      await this.initializeServices();

      // Initialize components
      await this.initializeComponents();

      // Setup global event handlers
      this.setupGlobalEventHandlers();

      // Load initial data
      await this.loadInitialData();

      // Setup UI
      this.setupUI();

      this.isInitialized = true;
      logInfo('Renderer', 'Renderer initialization completed successfully');

    } catch (error) {
      logError('Renderer', `Initialization failed: ${error.message}`);
      this.handleInitializationError(error);
    }
  }

  /**
   * Initialize core services
   */
  async initializeServices() {
    logInfo('Renderer', 'Initializing services...');

    // Event service (first, as others depend on it)
    this.services.eventService = new EventService();

    // Storage service
    this.services.storageService = new StorageService();

    // Data service
    this.services.dataService = new DataService(
      this.services.storageService,
      this.services.eventService
    );

    logInfo('Renderer', 'Services initialized successfully');
  }

  /**
   * Initialize UI components
   */
  async initializeComponents() {
    logInfo('Renderer', 'Initializing components...');

    // Tab manager
    this.components.tabManager = new TabManager(
      this.services.dataService,
      this.services.eventService
    );

    // Website manager
    this.components.websiteManager = new WebsiteManager(
      this.services.dataService,
      this.services.eventService,
      this.components.tabManager
    );

    logInfo('Renderer', 'Components initialized successfully');
  }

  /**
   * Setup global event handlers
   */
  setupGlobalEventHandlers() {
    // App ready event
    this.services.eventService.on('app-ready', () => {
      this.onAppReady();
    });

    // Data events
    this.services.eventService.on('data-loaded', () => {
      this.onDataLoaded();
    });

    // Error handling
    window.addEventListener('error', (event) => {
      logError('Renderer', `Global error: ${event.error.message}`);
    });

    window.addEventListener('unhandledrejection', (event) => {
      logError('Renderer', `Unhandled promise rejection: ${event.reason}`);
    });

    // Zoom events
    this.services.eventService.on('zoom-in', () => {
      this.zoomIn();
    });

    this.services.eventService.on('zoom-out', () => {
      this.zoomOut();
    });

    this.services.eventService.on('reset-zoom', () => {
      this.resetZoom();
    });

    // Reload current site
    this.services.eventService.on('reload-current-site', () => {
      this.components.tabManager.reloadActiveTab();
    });

    // Clear all data
    this.services.eventService.on('clear-all-data', () => {
      this.clearAllData();
    });

    // Settings
    this.services.eventService.on('show-settings', () => {
      this.showSettings();
    });

    // History
    this.services.eventService.on('show-history', () => {
      this.showHistory();
    });

    // Downloads
    this.services.eventService.on('show-downloads', () => {
      this.showDownloads();
    });

    // Find
    this.services.eventService.on('show-find', () => {
      this.showFind();
    });

    // Dev tools
    this.services.eventService.on('toggle-dev-tools', () => {
      this.toggleDevTools();
    });
  }

  /**
   * Load initial data
   */
  async loadInitialData() {
    logInfo('Renderer', 'Loading initial data...');
    await this.services.dataService.initialize();
  }

  /**
   * Setup initial UI state
   */
  setupUI() {
    // Render website list
    this.components.websiteManager.renderWebsiteList();
    this.components.websiteManager.updateStats();

    // Create initial tab if no websites or if auto-load is disabled
    if (this.services.dataService.websites.length === 0) {
      this.showWelcomeScreen();
    } else if (this.services.dataService.settings.autoLoadLastWebsite) {
      this.loadLastWebsite();
    } else {
      this.components.tabManager.createNewTab();
    }

    // Setup navigation
    this.setupNavigation();

    // Setup modals
    this.setupModals();
  }

  /**
   * Setup navigation controls
   */
  setupNavigation() {
    const backBtn = document.getElementById('backBtn');
    const forwardBtn = document.getElementById('forwardBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const homeBtn = document.getElementById('homeBtn');
    const urlInput = document.getElementById('urlInput');
    const goBtn = document.getElementById('goBtn');

    if (backBtn) {
      backBtn.addEventListener('click', () => {
        this.components.tabManager.goBackActiveTab();
      });
    }

    if (forwardBtn) {
      forwardBtn.addEventListener('click', () => {
        this.components.tabManager.goForwardActiveTab();
      });
    }

    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.components.tabManager.reloadActiveTab();
      });
    }

    if (homeBtn) {
      homeBtn.addEventListener('click', () => {
        const homepage = this.services.dataService.settings.homepage || 'about:blank';
        this.components.tabManager.navigateActiveTab(homepage);
      });
    }

    if (urlInput) {
      urlInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          this.navigateToUrl(urlInput.value);
        }
      });
    }

    if (goBtn) {
      goBtn.addEventListener('click', () => {
        if (urlInput) {
          this.navigateToUrl(urlInput.value);
        }
      });
    }
  }

  /**
   * Setup modal dialogs
   */
  setupModals() {
    // Add website modal
    this.setupAddWebsiteModal();
    
    // Settings modal
    this.setupSettingsModal();
    
    // History modal
    this.setupHistoryModal();
    
    // Downloads modal
    this.setupDownloadsModal();
  }

  /**
   * Setup add website modal
   */
  setupAddWebsiteModal() {
    const modal = document.getElementById('addWebsiteModal');
    const form = document.getElementById('addWebsiteForm');
    const closeBtn = document.getElementById('closeAddModal');
    const cancelBtn = document.getElementById('cancelAddBtn');

    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleAddWebsite();
      });
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideModal(modal);
      });
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        this.hideModal(modal);
      });
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideModal(modal);
        }
      });
    }
  }

  /**
   * Handle add website form submission
   */
  handleAddWebsite() {
    const nameInput = document.getElementById('websiteName');
    const urlInput = document.getElementById('websiteUrl');
    const categorySelect = document.getElementById('websiteCategory');
    const favoriteCheckbox = document.getElementById('websiteFavorite');

    if (!nameInput || !urlInput) return;

    try {
      const websiteData = {
        name: nameInput.value.trim(),
        url: urlInput.value.trim(),
        category: categorySelect ? categorySelect.value : 'general',
        isFavorite: favoriteCheckbox ? favoriteCheckbox.checked : false
      };

      this.services.dataService.addWebsite(websiteData);
      this.hideModal(document.getElementById('addWebsiteModal'));
      
      // Show success message
      this.showNotification('Website added successfully!', 'success');

    } catch (error) {
      this.showNotification(error.message, 'error');
    }
  }

  /**
   * Navigate to URL
   * @param {string} url - URL to navigate to
   */
  navigateToUrl(url) {
    if (!url || url.trim().length === 0) return;

    // Format URL
    let formattedUrl = url.trim();
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      // Check if it looks like a domain
      if (formattedUrl.includes('.') && !formattedUrl.includes(' ')) {
        formattedUrl = `https://${formattedUrl}`;
      } else {
        // Treat as search query
        formattedUrl = `https://www.google.com/search?q=${encodeURIComponent(formattedUrl)}`;
      }
    }

    this.components.tabManager.navigateActiveTab(formattedUrl);
  }

  /**
   * Show welcome screen
   */
  showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
      welcomeScreen.style.display = 'flex';
    }
  }

  /**
   * Load last website
   */
  async loadLastWebsite() {
    try {
      const lastWebsiteId = await this.services.storageService.getLastWebsite();
      if (lastWebsiteId) {
        const website = this.services.dataService.getWebsiteById(lastWebsiteId);
        if (website) {
          this.components.websiteManager.openWebsite(website);
          return;
        }
      }
    } catch (error) {
      logError('Renderer', `Error loading last website: ${error.message}`);
    }
    
    // Fallback to new tab
    this.components.tabManager.createNewTab();
  }

  /**
   * Handle app ready event
   */
  onAppReady() {
    logInfo('Renderer', 'App ready event received');
  }

  /**
   * Handle data loaded event
   */
  onDataLoaded() {
    logInfo('Renderer', 'Data loaded event received');
    this.components.websiteManager.renderWebsiteList();
    this.components.websiteManager.updateStats();
  }

  /**
   * Handle initialization error
   * @param {Error} error - Initialization error
   */
  handleInitializationError(error) {
    // Show error message to user
    document.body.innerHTML = `
      <div class="error-screen">
        <h1>Initialization Error</h1>
        <p>Failed to initialize Unrestricted Browser:</p>
        <pre>${error.message}</pre>
        <button onclick="location.reload()">Retry</button>
      </div>
    `;
  }

  // Utility methods

  zoomIn() {
    const webview = this.components.tabManager.getActiveWebview();
    if (webview) {
      const currentZoom = webview.getZoomFactor();
      webview.setZoomFactor(Math.min(currentZoom + 0.1, 3.0));
    }
  }

  zoomOut() {
    const webview = this.components.tabManager.getActiveWebview();
    if (webview) {
      const currentZoom = webview.getZoomFactor();
      webview.setZoomFactor(Math.max(currentZoom - 0.1, 0.25));
    }
  }

  resetZoom() {
    const webview = this.components.tabManager.getActiveWebview();
    if (webview) {
      webview.setZoomFactor(1.0);
    }
  }

  async clearAllData() {
    const result = await window.electronAPI.showMessageBox({
      type: 'warning',
      buttons: ['Cancel', 'Clear All Data'],
      defaultId: 0,
      title: 'Clear All Data',
      message: 'Are you sure you want to clear all data? This action cannot be undone.',
      detail: 'This will remove all websites, history, downloads, and settings.'
    });

    if (result.response === 1) {
      await this.services.dataService.clearAllData();
      location.reload();
    }
  }

  showSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  showHistory() {
    const modal = document.getElementById('historyModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  showDownloads() {
    const modal = document.getElementById('downloadsModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  showFind() {
    const findBar = document.getElementById('findBar');
    if (findBar) {
      findBar.style.display = 'flex';
      const findInput = document.getElementById('findInput');
      if (findInput) {
        findInput.focus();
      }
    }
  }

  toggleDevTools() {
    const webview = this.components.tabManager.getActiveWebview();
    if (webview) {
      if (webview.isDevToolsOpened()) {
        webview.closeDevTools();
      } else {
        webview.openDevTools();
      }
    }
  }

  hideModal(modal) {
    if (modal) {
      modal.style.display = 'none';
    }
  }

  showNotification(message, type = 'info') {
    // Simple notification implementation
    console.log(`[${type.toUpperCase()}] ${message}`);
  }

  setupSettingsModal() {
    // Implementation for settings modal
  }

  setupHistoryModal() {
    // Implementation for history modal
  }

  setupDownloadsModal() {
    // Implementation for downloads modal
  }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const app = new UnrestrictedBrowserRenderer();
  app.initialize();
  
  // Make app globally available for debugging
  window.browserApp = app;
});
