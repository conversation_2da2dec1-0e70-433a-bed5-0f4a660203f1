<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unrestricted Browser</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Side Panel -->
        <div class="side-panel" id="sidePanel">
            <div class="panel-header">
                <h2><i class="fas fa-globe"></i> Websites</h2>
                <div class="panel-controls">
                    <button class="btn-icon" id="addWebsiteBtn" title="Add Website">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn-icon" id="settingsBtn" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
            
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search websites..." class="search-input">
                <i class="fas fa-search search-icon"></i>
            </div>

            <div class="website-list" id="websiteList">
                <!-- Websites will be dynamically added here -->
            </div>

            <div class="panel-footer">
                <div class="stats">
                    <span id="websiteCount">0 websites</span>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Tab Bar -->
            <div class="tab-bar">
                <div class="tabs-container" id="tabsContainer">
                    <!-- Tabs will be dynamically added here -->
                </div>
                <button class="btn-icon new-tab-btn" id="newTabBtn" title="New Tab">
                    <i class="fas fa-plus"></i>
                </button>
            </div>

            <!-- Top Bar -->
            <div class="top-bar">
                <div class="navigation-controls">
                    <button class="btn-icon" id="backBtn" title="Back" disabled>
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="btn-icon" id="forwardBtn" title="Forward" disabled>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <button class="btn-icon" id="refreshBtn" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn-icon" id="homeBtn" title="Home">
                        <i class="fas fa-home"></i>
                    </button>
                </div>

                <div class="url-bar">
                    <div class="security-indicator" id="securityIndicator">
                        <i class="fas fa-lock" title="Secure"></i>
                    </div>
                    <input type="text" id="urlInput" placeholder="Enter URL or search..." class="url-input">
                    <button class="btn-icon" id="goBtn" title="Go">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <button class="btn-icon" id="findBtn" title="Find in Page">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="view-controls">
                    <button class="btn-icon" id="historyBtn" title="History">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn-icon" id="downloadsBtn" title="Downloads">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn-icon" id="devToolsBtn" title="Developer Tools">
                        <i class="fas fa-code"></i>
                    </button>
                    <button class="btn-icon" id="zoomOutBtn" title="Zoom Out">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level" id="zoomLevel">100%</span>
                    <button class="btn-icon" id="zoomInBtn" title="Zoom In">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="btn-icon" id="fullscreenBtn" title="Toggle Fullscreen">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>

            <!-- Find Bar -->
            <div class="find-bar" id="findBar">
                <input type="text" id="findInput" placeholder="Find in page..." class="find-input">
                <button class="btn-icon" id="findPrevBtn" title="Previous">
                    <i class="fas fa-chevron-up"></i>
                </button>
                <button class="btn-icon" id="findNextBtn" title="Next">
                    <i class="fas fa-chevron-down"></i>
                </button>
                <span class="find-results" id="findResults">0/0</span>
                <button class="btn-icon" id="closeFindBtn" title="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Web View Container -->
            <div class="webview-container" id="webviewContainer">
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="welcome-content">
                        <i class="fas fa-globe-americas welcome-icon"></i>
                        <h1>Welcome to Unrestricted Browser</h1>
                        <p>Add websites to your collection and browse without restrictions</p>
                        <button class="btn-primary" id="welcomeAddBtn">
                            <i class="fas fa-plus"></i> Add Your First Website
                        </button>
                    </div>
                </div>
                <!-- Webviews will be dynamically created here -->
            </div>

            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator">
                <div class="loading-spinner"></div>
                <span>Loading...</span>
            </div>
        </div>
    </div>

    <!-- Add Website Modal -->
    <div class="modal-overlay" id="addWebsiteModal">
        <div class="modal">
            <div class="modal-header">
                <h3>Add Website</h3>
                <button class="btn-icon modal-close" id="closeAddModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addWebsiteForm">
                    <div class="form-group">
                        <label for="websiteName">Name</label>
                        <input type="text" id="websiteName" placeholder="Website name" required>
                    </div>
                    <div class="form-group">
                        <label for="websiteUrl">URL</label>
                        <input type="url" id="websiteUrl" placeholder="https://example.com" required>
                    </div>
                    <div class="form-group">
                        <label for="websiteCategory">Category</label>
                        <select id="websiteCategory">
                            <option value="general">General</option>
                            <option value="social">Social Media</option>
                            <option value="news">News</option>
                            <option value="entertainment">Entertainment</option>
                            <option value="work">Work</option>
                            <option value="education">Education</option>
                            <option value="shopping">Shopping</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="websiteFavorite">
                            Mark as favorite
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="cancelAddBtn">Cancel</button>
                <button type="submit" form="addWebsiteForm" class="btn-primary">Add Website</button>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal-overlay" id="settingsModal">
        <div class="modal">
            <div class="modal-header">
                <h3>Settings</h3>
                <button class="btn-icon modal-close" id="closeSettingsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h4>General</h4>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="autoLoadLastWebsite">
                            Auto-load last visited website on startup
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="showNotifications">
                            Show notifications
                        </label>
                    </div>
                </div>
                <div class="settings-section">
                    <h4>Privacy & Security</h4>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="clearDataOnExit">
                            Clear browsing data on exit
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="blockAds">
                            Block advertisements (experimental)
                        </label>
                    </div>
                </div>
                <div class="settings-section">
                    <h4>Data Management</h4>
                    <div class="form-group">
                        <button class="btn-secondary" id="exportDataBtn">
                            <i class="fas fa-download"></i> Export Websites
                        </button>
                        <button class="btn-secondary" id="importDataBtn">
                            <i class="fas fa-upload"></i> Import Websites
                        </button>
                    </div>
                    <div class="form-group">
                        <button class="btn-danger" id="clearAllDataBtn">
                            <i class="fas fa-trash"></i> Clear All Data
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-primary" id="saveSettingsBtn">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- History Modal -->
    <div class="modal-overlay" id="historyModal">
        <div class="modal large-modal">
            <div class="modal-header">
                <h3>Browsing History</h3>
                <button class="btn-icon modal-close" id="closeHistoryModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <input type="text" id="historySearch" placeholder="Search history..." class="search-input">
                    <button class="btn-secondary" id="clearHistoryBtn">
                        <i class="fas fa-trash"></i> Clear History
                    </button>
                </div>
                <div class="history-list" id="historyList">
                    <!-- History items will be dynamically added here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Downloads Modal -->
    <div class="modal-overlay" id="downloadsModal">
        <div class="modal large-modal">
            <div class="modal-header">
                <h3>Downloads</h3>
                <button class="btn-icon modal-close" id="closeDownloadsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="downloads-controls">
                    <button class="btn-secondary" id="clearDownloadsBtn">
                        <i class="fas fa-trash"></i> Clear Downloads
                    </button>
                    <button class="btn-secondary" id="openDownloadsFolderBtn">
                        <i class="fas fa-folder-open"></i> Open Downloads Folder
                    </button>
                </div>
                <div class="downloads-list" id="downloadsList">
                    <div class="empty-state">
                        <i class="fas fa-download"></i>
                        <p>No downloads yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div class="context-menu" id="contextMenu">
        <div class="context-item" id="editWebsite">
            <i class="fas fa-edit"></i> Edit
        </div>
        <div class="context-item" id="toggleFavorite">
            <i class="fas fa-star"></i> Toggle Favorite
        </div>
        <div class="context-item" id="copyUrl">
            <i class="fas fa-copy"></i> Copy URL
        </div>
        <div class="context-item context-separator"></div>
        <div class="context-item context-danger" id="deleteWebsite">
            <i class="fas fa-trash"></i> Delete
        </div>
    </div>

    <!-- Tab Context Menu -->
    <div class="context-menu" id="tabContextMenu">
        <div class="context-item" id="closeTab">
            <i class="fas fa-times"></i> Close Tab
        </div>
        <div class="context-item" id="closeOtherTabs">
            <i class="fas fa-times-circle"></i> Close Other Tabs
        </div>
        <div class="context-item" id="duplicateTab">
            <i class="fas fa-copy"></i> Duplicate Tab
        </div>
        <div class="context-item context-separator"></div>
        <div class="context-item" id="reloadTab">
            <i class="fas fa-sync-alt"></i> Reload Tab
        </div>
    </div>

    <script type="module" src="renderer/index.js"></script>
</body>
</html>