/**
 * Event service for managing application events
 */

import { logError, logInfo } from '../../shared/utils.js';

export class EventService {
  constructor() {
    this.listeners = new Map();
    this.electronAPI = window.electronAPI;
    this.setupElectronListeners();
  }

  /**
   * Setup Electron IPC event listeners
   */
  setupElectronListeners() {
    // App events
    this.electronAPI.onAppReady(() => {
      this.emit('app-ready');
    });

    // Menu events
    this.electronAPI.onShowAddWebsiteDialog(() => {
      this.emit('show-add-website-dialog');
    });

    this.electronAPI.onImportWebsites((event, filePath) => {
      this.emit('import-websites', filePath);
    });

    this.electronAPI.onExportWebsites((event, filePath) => {
      this.emit('export-websites', filePath);
    });

    this.electronAPI.onReloadCurrentSite(() => {
      this.emit('reload-current-site');
    });

    this.electronAPI.onZoomIn(() => {
      this.emit('zoom-in');
    });

    this.electronAPI.onZoomOut(() => {
      this.emit('zoom-out');
    });

    this.electronAPI.onResetZoom(() => {
      this.emit('reset-zoom');
    });

    this.electronAPI.onClearAllData(() => {
      this.emit('clear-all-data');
    });

    this.electronAPI.onShowSettings(() => {
      this.emit('show-settings');
    });

    this.electronAPI.onShowHistory(() => {
      this.emit('show-history');
    });

    this.electronAPI.onShowDownloads(() => {
      this.emit('show-downloads');
    });

    this.electronAPI.onShowFind(() => {
      this.emit('show-find');
    });

    this.electronAPI.onToggleDevTools(() => {
      this.emit('toggle-dev-tools');
    });

    // Tab events
    this.electronAPI.onNewTab(() => {
      this.emit('new-tab');
    });

    this.electronAPI.onCloseTab(() => {
      this.emit('close-tab');
    });

    this.electronAPI.onNextTab(() => {
      this.emit('next-tab');
    });

    this.electronAPI.onPrevTab(() => {
      this.emit('prev-tab');
    });

    // Download events
    this.electronAPI.onDownloadStarted((event, data) => {
      this.emit('download-started', data);
    });

    this.electronAPI.onDownloadProgress((event, data) => {
      this.emit('download-progress', data);
    });

    this.electronAPI.onDownloadCompleted((event, data) => {
      this.emit('download-completed', data);
    });

    this.electronAPI.onDownloadFailed((event, data) => {
      this.emit('download-failed', data);
    });

    this.electronAPI.onDownloadInterrupted((event, data) => {
      this.emit('download-interrupted', data);
    });

    this.electronAPI.onDownloadPaused((event, data) => {
      this.emit('download-paused', data);
    });

    logInfo('EventService', 'Electron listeners setup completed');
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    
    this.listeners.get(event).add(callback);
    
    // Return unsubscribe function
    return () => {
      this.off(event, callback);
    };
  }

  /**
   * Add one-time event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  once(event, callback) {
    const onceCallback = (...args) => {
      callback(...args);
      this.off(event, onceCallback);
    };
    
    return this.on(event, onceCallback);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
      
      // Clean up empty event sets
      if (this.listeners.get(event).size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * Emit event
   * @param {string} event - Event name
   * @param {...any} args - Event arguments
   */
  emit(event, ...args) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      callbacks.forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          logError('EventService', `Error in event callback for ${event}: ${error.message}`);
        }
      });
    }
  }

  /**
   * Remove all listeners for an event
   * @param {string} event - Event name
   */
  removeAllListeners(event) {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }

  /**
   * Get listener count for an event
   * @param {string} event - Event name
   * @returns {number} Number of listeners
   */
  listenerCount(event) {
    return this.listeners.has(event) ? this.listeners.get(event).size : 0;
  }

  /**
   * Get all event names
   * @returns {string[]} Array of event names
   */
  eventNames() {
    return Array.from(this.listeners.keys());
  }

  /**
   * Create a promise that resolves when an event is emitted
   * @param {string} event - Event name
   * @param {number} timeout - Timeout in milliseconds (optional)
   * @returns {Promise} Promise that resolves with event data
   */
  waitFor(event, timeout = null) {
    return new Promise((resolve, reject) => {
      let timeoutId = null;
      
      const unsubscribe = this.once(event, (...args) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        resolve(args.length === 1 ? args[0] : args);
      });
      
      if (timeout) {
        timeoutId = setTimeout(() => {
          unsubscribe();
          reject(new Error(`Event ${event} timeout after ${timeout}ms`));
        }, timeout);
      }
    });
  }

  /**
   * Create an event emitter for a specific namespace
   * @param {string} namespace - Namespace for events
   * @returns {Object} Namespaced event emitter
   */
  namespace(namespace) {
    return {
      on: (event, callback) => this.on(`${namespace}:${event}`, callback),
      once: (event, callback) => this.once(`${namespace}:${event}`, callback),
      off: (event, callback) => this.off(`${namespace}:${event}`, callback),
      emit: (event, ...args) => this.emit(`${namespace}:${event}`, ...args),
      removeAllListeners: (event) => this.removeAllListeners(event ? `${namespace}:${event}` : null)
    };
  }

  /**
   * Cleanup all event listeners
   */
  cleanup() {
    this.listeners.clear();
    
    // Remove Electron listeners
    if (this.electronAPI && this.electronAPI.removeAllListeners) {
      const channels = [
        'app-ready', 'show-add-website-dialog', 'import-websites', 'export-websites',
        'reload-current-site', 'zoom-in', 'zoom-out', 'reset-zoom', 'clear-all-data',
        'show-settings', 'show-history', 'show-downloads', 'show-find', 'toggle-dev-tools',
        'new-tab', 'close-tab', 'next-tab', 'prev-tab',
        'download-started', 'download-progress', 'download-completed', 'download-failed',
        'download-interrupted', 'download-paused'
      ];
      
      channels.forEach(channel => {
        this.electronAPI.removeAllListeners(channel);
      });
    }
    
    logInfo('EventService', 'Event service cleaned up');
  }
}
