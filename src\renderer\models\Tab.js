/**
 * Tab model class
 */

import { generateId, formatUrl, extractDomain, getFaviconUrl } from '../../shared/utils.js';

export class Tab {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.url = data.url || 'about:blank';
    this.title = data.title || 'New Tab';
    this.favicon = data.favicon || null;
    this.isLoading = data.isLoading || false;
    this.canGoBack = data.canGoBack || false;
    this.canGoForward = data.canGoForward || false;
    this.zoomLevel = data.zoomLevel || 1.0;
    this.isActive = data.isActive || false;
    this.webviewId = data.webviewId || null;
    this.websiteId = data.websiteId || null;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.lastActiveAt = data.lastActiveAt || new Date().toISOString();
    this.history = data.history || [];
    this.historyIndex = data.historyIndex || -1;
  }

  /**
   * Navigate to a URL
   * @param {string} url - URL to navigate to
   */
  navigateTo(url) {
    const formattedUrl = formatUrl(url);
    this.url = formattedUrl;
    this.isLoading = true;
    this.lastActiveAt = new Date().toISOString();
    
    // Add to history if it's a new URL
    if (this.history[this.historyIndex] !== formattedUrl) {
      // Remove any forward history
      this.history = this.history.slice(0, this.historyIndex + 1);
      this.history.push(formattedUrl);
      this.historyIndex = this.history.length - 1;
    }
    
    this.updateNavigationState();
  }

  /**
   * Update tab title
   * @param {string} title - New title
   */
  updateTitle(title) {
    this.title = title || this.getDisplayTitle();
  }

  /**
   * Update favicon
   * @param {string} favicon - Favicon URL
   */
  updateFavicon(favicon) {
    this.favicon = favicon || getFaviconUrl(this.url);
  }

  /**
   * Set loading state
   * @param {boolean} isLoading - Loading state
   */
  setLoading(isLoading) {
    this.isLoading = isLoading;
  }

  /**
   * Set active state
   * @param {boolean} isActive - Active state
   */
  setActive(isActive) {
    this.isActive = isActive;
    if (isActive) {
      this.lastActiveAt = new Date().toISOString();
    }
  }

  /**
   * Update zoom level
   * @param {number} zoomLevel - New zoom level
   */
  setZoomLevel(zoomLevel) {
    this.zoomLevel = Math.max(0.25, Math.min(5.0, zoomLevel));
  }

  /**
   * Go back in history
   * @returns {string|null} Previous URL or null if can't go back
   */
  goBack() {
    if (this.canGoBack && this.historyIndex > 0) {
      this.historyIndex--;
      this.url = this.history[this.historyIndex];
      this.updateNavigationState();
      return this.url;
    }
    return null;
  }

  /**
   * Go forward in history
   * @returns {string|null} Next URL or null if can't go forward
   */
  goForward() {
    if (this.canGoForward && this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      this.url = this.history[this.historyIndex];
      this.updateNavigationState();
      return this.url;
    }
    return null;
  }

  /**
   * Update navigation state (back/forward buttons)
   */
  updateNavigationState() {
    this.canGoBack = this.historyIndex > 0;
    this.canGoForward = this.historyIndex < this.history.length - 1;
  }

  /**
   * Get display title for the tab
   * @returns {string} Display title
   */
  getDisplayTitle() {
    if (this.title && this.title !== 'New Tab') {
      return this.title;
    }
    
    if (this.url === 'about:blank') {
      return 'New Tab';
    }
    
    return extractDomain(this.url) || 'Loading...';
  }

  /**
   * Get domain name
   * @returns {string} Domain name
   */
  getDomain() {
    return extractDomain(this.url);
  }

  /**
   * Check if tab is on a secure connection
   * @returns {boolean} True if HTTPS
   */
  isSecure() {
    return this.url.startsWith('https://');
  }

  /**
   * Get tab data for serialization
   * @returns {Object} Serializable data
   */
  toJSON() {
    return {
      id: this.id,
      url: this.url,
      title: this.title,
      favicon: this.favicon,
      isLoading: this.isLoading,
      canGoBack: this.canGoBack,
      canGoForward: this.canGoForward,
      zoomLevel: this.zoomLevel,
      isActive: this.isActive,
      webviewId: this.webviewId,
      websiteId: this.websiteId,
      createdAt: this.createdAt,
      lastActiveAt: this.lastActiveAt,
      history: this.history,
      historyIndex: this.historyIndex
    };
  }

  /**
   * Create Tab instance from JSON data
   * @param {Object} data - JSON data
   * @returns {Tab} Tab instance
   */
  static fromJSON(data) {
    return new Tab(data);
  }

  /**
   * Create a new tab with a specific URL
   * @param {string} url - Initial URL
   * @param {string} websiteId - Associated website ID
   * @returns {Tab} New tab instance
   */
  static createWithUrl(url, websiteId = null) {
    const formattedUrl = formatUrl(url);
    return new Tab({
      url: formattedUrl,
      websiteId,
      history: [formattedUrl],
      historyIndex: 0
    });
  }

  /**
   * Create a blank new tab
   * @returns {Tab} New blank tab instance
   */
  static createBlank() {
    return new Tab({
      url: 'about:blank',
      title: 'New Tab'
    });
  }
}
