/**
 * Session management module
 */

const { session } = require('electron');

class SessionManager {
  constructor() {
    this.defaultSession = session.defaultSession;
    this.setupSession();
  }

  /**
   * Setup session configuration
   */
  setupSession() {
    this.configureUserAgent();
    this.configurePermissions();
    this.configureCertificates();
    this.configureWebRequest();
    this.configureWebSecurity();
  }

  /**
   * Configure user agent for better site compatibility
   */
  configureUserAgent() {
    const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    this.defaultSession.setUserAgent(userAgent);
  }

  /**
   * Configure permission handling
   */
  configurePermissions() {
    this.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      // Allow most permissions for unrestricted browsing
      const allowedPermissions = [
        'media',
        'geolocation',
        'notifications',
        'fullscreen',
        'pointerLock',
        'keyboardLock',
        'clipboard-read',
        'clipboard-write',
        'camera',
        'microphone',
        'midi',
        'background-sync',
        'push',
        'persistent-storage'
      ];
      
      if (allowedPermissions.includes(permission)) {
        callback(true);
      } else {
        callback(false);
      }
    });

    // Handle permission check
    this.defaultSession.setPermissionCheckHandler((webContents, permission, requestingOrigin, details) => {
      const allowedPermissions = [
        'media',
        'geolocation',
        'notifications',
        'fullscreen',
        'pointerLock',
        'keyboardLock',
        'clipboard-read',
        'clipboard-write'
      ];
      
      return allowedPermissions.includes(permission);
    });
  }

  /**
   * Configure certificate handling for unrestricted browsing
   */
  configureCertificates() {
    this.defaultSession.setCertificateVerifyProc((request, callback) => {
      // Allow all certificates for unrestricted browsing
      // In production, you might want to implement proper certificate validation
      callback(0);
    });
  }

  /**
   * Configure web request handling
   */
  configureWebRequest() {
    // Modify headers for better compatibility
    this.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      // Add/modify headers to help with site compatibility
      details.requestHeaders['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
      
      // Add common headers that some sites expect
      if (!details.requestHeaders['Accept-Language']) {
        details.requestHeaders['Accept-Language'] = 'en-US,en;q=0.9';
      }
      
      if (!details.requestHeaders['Accept-Encoding']) {
        details.requestHeaders['Accept-Encoding'] = 'gzip, deflate, br';
      }
      
      callback({ requestHeaders: details.requestHeaders });
    });

    // Handle response headers if needed
    this.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      // Remove X-Frame-Options to allow embedding
      if (details.responseHeaders['X-Frame-Options']) {
        delete details.responseHeaders['X-Frame-Options'];
      }
      
      if (details.responseHeaders['x-frame-options']) {
        delete details.responseHeaders['x-frame-options'];
      }
      
      // Remove CSP headers that might block functionality
      if (details.responseHeaders['Content-Security-Policy']) {
        delete details.responseHeaders['Content-Security-Policy'];
      }
      
      if (details.responseHeaders['content-security-policy']) {
        delete details.responseHeaders['content-security-policy'];
      }
      
      callback({ responseHeaders: details.responseHeaders });
    });
  }

  /**
   * Configure web security settings
   */
  configureWebSecurity() {
    // Protocol schemes should be registered before app ready
    // This is handled in the main process initialization
  }

  /**
   * Clear session data
   * @param {Object} options - Clear options
   */
  async clearData(options = {}) {
    const defaultOptions = {
      storages: [
        'appcache',
        'cookies',
        'filesystem',
        'indexdb',
        'localstorage',
        'shadercache',
        'websql',
        'serviceworkers',
        'cachestorage'
      ]
    };

    const clearOptions = { ...defaultOptions, ...options };
    
    try {
      await this.defaultSession.clearStorageData(clearOptions);
      console.log('Session data cleared successfully');
    } catch (error) {
      console.error('Error clearing session data:', error);
    }
  }

  /**
   * Clear cache
   */
  async clearCache() {
    try {
      await this.defaultSession.clearCache();
      console.log('Cache cleared successfully');
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Clear cookies
   * @param {string} url - URL to clear cookies for (optional)
   */
  async clearCookies(url = null) {
    try {
      if (url) {
        await this.defaultSession.cookies.remove(url, '');
      } else {
        const cookies = await this.defaultSession.cookies.get({});
        for (const cookie of cookies) {
          await this.defaultSession.cookies.remove(
            `http${cookie.secure ? 's' : ''}://${cookie.domain}${cookie.path}`,
            cookie.name
          );
        }
      }
      console.log('Cookies cleared successfully');
    } catch (error) {
      console.error('Error clearing cookies:', error);
    }
  }

  /**
   * Set proxy configuration
   * @param {Object} proxyConfig - Proxy configuration
   */
  async setProxy(proxyConfig) {
    try {
      await this.defaultSession.setProxy(proxyConfig);
      console.log('Proxy configuration set successfully');
    } catch (error) {
      console.error('Error setting proxy configuration:', error);
    }
  }

  /**
   * Get session information
   * @returns {Object} Session information
   */
  getSessionInfo() {
    return {
      userAgent: this.defaultSession.getUserAgent(),
      canUseProxy: this.defaultSession.resolveProxy !== undefined,
      version: process.versions.electron
    };
  }

  /**
   * Enable/disable web security
   * @param {boolean} enabled - Whether to enable web security
   */
  setWebSecurity(enabled) {
    // Note: This affects new webviews created after this call
    this.webSecurityEnabled = enabled;
  }

  /**
   * Configure content blocking (ad blocking, etc.)
   * @param {Object} rules - Blocking rules
   */
  configureContentBlocking(rules = {}) {
    if (rules.blockAds) {
      // Basic ad blocking - block common ad domains
      const adDomains = [
        'doubleclick.net',
        'googleadservices.com',
        'googlesyndication.com',
        'amazon-adsystem.com'
      ];

      this.defaultSession.webRequest.onBeforeRequest((details, callback) => {
        const url = new URL(details.url);
        const shouldBlock = adDomains.some(domain => url.hostname.includes(domain));
        
        if (shouldBlock) {
          callback({ cancel: true });
        } else {
          callback({});
        }
      });
    }
  }

  /**
   * Setup custom protocol handlers
   */
  setupProtocolHandlers() {
    // Register custom protocols if needed
    this.defaultSession.protocol.registerFileProtocol('app', (request, callback) => {
      const url = request.url.substr(6); // Remove 'app://' prefix
      callback({ path: path.normalize(`${__dirname}/${url}`) });
    });
  }

  /**
   * Cleanup session resources
   */
  cleanup() {
    // Remove all listeners and clear data if needed
    this.defaultSession.webRequest.onBeforeSendHeaders(null);
    this.defaultSession.webRequest.onHeadersReceived(null);
    this.defaultSession.webRequest.onBeforeRequest(null);
  }
}

module.exports = SessionManager;
