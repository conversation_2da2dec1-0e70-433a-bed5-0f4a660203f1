/**
 * Website management component
 */

import { Website } from '../models/Website.js';
import { WEBSITE_CATEGORIES } from '../../shared/constants.js';
import { escapeHtml, debounce } from '../../shared/utils.js';

export class WebsiteManager {
  constructor(dataService, eventService, tabManager) {
    this.dataService = dataService;
    this.eventService = eventService;
    this.tabManager = tabManager;
    this.websiteList = document.getElementById('websiteList');
    this.searchInput = document.getElementById('searchInput');
    this.addWebsiteBtn = document.getElementById('addWebsiteBtn');
    this.websiteCount = document.getElementById('websiteCount');
    this.currentFilter = '';
    this.currentCategory = 'all';
    
    this.setupEventListeners();
    this.setupSearch();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Add website button
    this.addWebsiteBtn.addEventListener('click', () => {
      this.showAddWebsiteDialog();
    });

    // Welcome screen add button
    const welcomeAddBtn = document.getElementById('welcomeAddBtn');
    if (welcomeAddBtn) {
      welcomeAddBtn.addEventListener('click', () => {
        this.showAddWebsiteDialog();
      });
    }

    // Event service listeners
    this.eventService.on('show-add-website-dialog', () => {
      this.showAddWebsiteDialog();
    });

    this.eventService.on('website-added', () => {
      this.renderWebsiteList();
      this.updateStats();
    });

    this.eventService.on('website-updated', () => {
      this.renderWebsiteList();
    });

    this.eventService.on('website-deleted', () => {
      this.renderWebsiteList();
      this.updateStats();
    });

    this.eventService.on('import-websites', (filePath) => {
      this.importWebsites(filePath);
    });

    this.eventService.on('export-websites', (filePath) => {
      this.exportWebsites(filePath);
    });
  }

  /**
   * Setup search functionality
   */
  setupSearch() {
    const debouncedSearch = debounce((query) => {
      this.currentFilter = query;
      this.renderWebsiteList();
    }, 300);

    this.searchInput.addEventListener('input', (e) => {
      debouncedSearch(e.target.value);
    });

    this.searchInput.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.searchInput.value = '';
        this.currentFilter = '';
        this.renderWebsiteList();
      }
    });
  }

  /**
   * Render the website list
   */
  renderWebsiteList() {
    if (!this.websiteList) return;

    const websites = this.getFilteredWebsites();
    this.websiteList.innerHTML = '';

    if (websites.length === 0) {
      this.renderEmptyState();
      return;
    }

    // Group websites by category if no filter
    if (!this.currentFilter) {
      this.renderGroupedWebsites(websites);
    } else {
      this.renderFlatWebsites(websites);
    }
  }

  /**
   * Get filtered websites
   * @returns {Website[]} Filtered websites
   */
  getFilteredWebsites() {
    let websites = this.dataService.websites;

    // Apply search filter
    if (this.currentFilter) {
      websites = this.dataService.searchWebsites(this.currentFilter);
    }

    // Apply category filter
    if (this.currentCategory !== 'all') {
      websites = websites.filter(w => w.category === this.currentCategory);
    }

    // Sort websites
    return websites.sort((a, b) => {
      // Favorites first
      if (a.isFavorite && !b.isFavorite) return -1;
      if (!a.isFavorite && b.isFavorite) return 1;
      
      // Then by visit count
      if (a.visitCount !== b.visitCount) {
        return b.visitCount - a.visitCount;
      }
      
      // Finally by name
      return a.name.localeCompare(b.name);
    });
  }

  /**
   * Render empty state
   */
  renderEmptyState() {
    const emptyState = document.createElement('div');
    emptyState.className = 'empty-state';
    
    if (this.currentFilter) {
      emptyState.innerHTML = `
        <i class="fas fa-search"></i>
        <p>No websites found for "${escapeHtml(this.currentFilter)}"</p>
        <button class="btn-secondary" onclick="document.getElementById('searchInput').value = ''; this.currentFilter = ''; this.renderWebsiteList();">
          Clear Search
        </button>
      `;
    } else {
      emptyState.innerHTML = `
        <i class="fas fa-globe"></i>
        <p>No websites added yet</p>
        <button class="btn-primary" onclick="this.showAddWebsiteDialog()">
          <i class="fas fa-plus"></i> Add Your First Website
        </button>
      `;
    }
    
    this.websiteList.appendChild(emptyState);
  }

  /**
   * Render websites grouped by category
   * @param {Website[]} websites - Websites to render
   */
  renderGroupedWebsites(websites) {
    const grouped = this.groupWebsitesByCategory(websites);
    
    Object.keys(grouped).forEach(category => {
      if (grouped[category].length === 0) return;
      
      // Category header
      const categoryHeader = document.createElement('div');
      categoryHeader.className = 'category-header';
      categoryHeader.innerHTML = `
        <h4>${this.getCategoryDisplayName(category)}</h4>
        <span class="category-count">${grouped[category].length}</span>
      `;
      this.websiteList.appendChild(categoryHeader);
      
      // Websites in category
      grouped[category].forEach(website => {
        this.renderWebsiteItem(website);
      });
    });
  }

  /**
   * Render websites in flat list
   * @param {Website[]} websites - Websites to render
   */
  renderFlatWebsites(websites) {
    websites.forEach(website => {
      this.renderWebsiteItem(website);
    });
  }

  /**
   * Render a single website item
   * @param {Website} website - Website to render
   */
  renderWebsiteItem(website) {
    const item = document.createElement('div');
    item.className = 'website-item';
    item.dataset.websiteId = website.id;
    
    if (website.isFavorite) {
      item.classList.add('favorite');
    }
    
    item.innerHTML = `
      <div class="website-favicon">
        ${website.favicon ? 
          `<img src="${website.favicon}" alt="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
           <i class="fas fa-globe" style="display: none;"></i>` :
          '<i class="fas fa-globe"></i>'
        }
      </div>
      <div class="website-info">
        <div class="website-name">${escapeHtml(website.name)}</div>
        <div class="website-url">${escapeHtml(website.getDomain())}</div>
        <div class="website-meta">
          <span class="visit-count">${website.visitCount} visits</span>
          ${website.lastVisited ? 
            `<span class="last-visited">Last: ${new Date(website.lastVisited).toLocaleDateString()}</span>` : 
            ''
          }
        </div>
      </div>
      <div class="website-actions">
        ${website.isFavorite ? '<i class="fas fa-star favorite-icon" title="Favorite"></i>' : ''}
        <button class="btn-icon" title="Open in new tab">
          <i class="fas fa-external-link-alt"></i>
        </button>
      </div>
    `;

    // Add event listeners
    item.addEventListener('click', (e) => {
      if (!e.target.closest('.website-actions')) {
        this.openWebsite(website);
      }
    });

    item.addEventListener('dblclick', () => {
      this.openWebsiteInNewTab(website);
    });

    item.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.showWebsiteContextMenu(e, website);
    });

    // New tab button
    const newTabBtn = item.querySelector('.website-actions .btn-icon');
    newTabBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.openWebsiteInNewTab(website);
    });

    this.websiteList.appendChild(item);
  }

  /**
   * Group websites by category
   * @param {Website[]} websites - Websites to group
   * @returns {Object} Grouped websites
   */
  groupWebsitesByCategory(websites) {
    const grouped = {};
    
    // Initialize all categories
    WEBSITE_CATEGORIES.forEach(category => {
      grouped[category] = [];
    });
    
    // Group websites
    websites.forEach(website => {
      if (!grouped[website.category]) {
        grouped[website.category] = [];
      }
      grouped[website.category].push(website);
    });
    
    return grouped;
  }

  /**
   * Get category display name
   * @param {string} category - Category key
   * @returns {string} Display name
   */
  getCategoryDisplayName(category) {
    const names = {
      general: 'General',
      social: 'Social Media',
      news: 'News',
      entertainment: 'Entertainment',
      work: 'Work',
      education: 'Education',
      shopping: 'Shopping',
      other: 'Other'
    };
    return names[category] || category;
  }

  /**
   * Open website in current tab
   * @param {Website} website - Website to open
   */
  openWebsite(website) {
    website.recordVisit();
    this.dataService.saveWebsites();
    
    // Get or create active tab
    let activeTab = this.dataService.getActiveTab();
    if (!activeTab) {
      activeTab = this.tabManager.createNewTab(website.url, website.id);
    } else {
      this.tabManager.navigateActiveTab(website.url);
    }
    
    this.eventService.emit('website-opened', website);
  }

  /**
   * Open website in new tab
   * @param {Website} website - Website to open
   */
  openWebsiteInNewTab(website) {
    website.recordVisit();
    this.dataService.saveWebsites();
    
    this.tabManager.createNewTab(website.url, website.id);
    this.eventService.emit('website-opened-new-tab', website);
  }

  /**
   * Show add website dialog
   */
  showAddWebsiteDialog() {
    const modal = document.getElementById('addWebsiteModal');
    if (modal) {
      modal.style.display = 'flex';
      
      // Clear form
      const form = document.getElementById('addWebsiteForm');
      if (form) {
        form.reset();
      }
      
      // Focus name input
      const nameInput = document.getElementById('websiteName');
      if (nameInput) {
        setTimeout(() => nameInput.focus(), 100);
      }
    }
  }

  /**
   * Show website context menu
   * @param {Event} event - Context menu event
   * @param {Website} website - Website
   */
  showWebsiteContextMenu(event, website) {
    this.eventService.emit('show-website-context-menu', { event, website });
  }

  /**
   * Update statistics
   */
  updateStats() {
    if (this.websiteCount) {
      const count = this.dataService.websites.length;
      this.websiteCount.textContent = `${count} website${count !== 1 ? 's' : ''}`;
    }
  }

  /**
   * Set category filter
   * @param {string} category - Category to filter by
   */
  setCategoryFilter(category) {
    this.currentCategory = category;
    this.renderWebsiteList();
  }

  /**
   * Export websites to file
   * @param {string} filePath - File path to export to
   */
  async exportWebsites(filePath) {
    try {
      const data = {
        websites: this.dataService.websites.map(w => w.toJSON()),
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      };
      
      const content = JSON.stringify(data, null, 2);
      await window.electronAPI.writeFile(filePath, content);
      
      this.eventService.emit('websites-exported', { filePath, count: data.websites.length });
    } catch (error) {
      this.eventService.emit('export-error', error);
    }
  }

  /**
   * Import websites from file
   * @param {string} filePath - File path to import from
   */
  async importWebsites(filePath) {
    try {
      const content = await window.electronAPI.readFile(filePath);
      const data = JSON.parse(content);
      
      if (data.websites && Array.isArray(data.websites)) {
        let importedCount = 0;
        
        data.websites.forEach(websiteData => {
          try {
            // Check if website already exists
            const existing = this.dataService.websites.find(w => w.url === websiteData.url);
            if (!existing) {
              this.dataService.addWebsite(websiteData);
              importedCount++;
            }
          } catch (error) {
            console.error('Error importing website:', error);
          }
        });
        
        this.eventService.emit('websites-imported', { filePath, count: importedCount });
      } else {
        throw new Error('Invalid file format');
      }
    } catch (error) {
      this.eventService.emit('import-error', error);
    }
  }

  /**
   * Search websites
   * @param {string} query - Search query
   */
  search(query) {
    this.searchInput.value = query;
    this.currentFilter = query;
    this.renderWebsiteList();
  }

  /**
   * Clear search
   */
  clearSearch() {
    this.searchInput.value = '';
    this.currentFilter = '';
    this.renderWebsiteList();
  }
}
