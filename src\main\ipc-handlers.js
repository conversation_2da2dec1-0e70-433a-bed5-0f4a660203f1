/**
 * IPC handlers module
 */

const { ipcMain, dialog, app, shell } = require('electron');
const fs = require('fs');
const path = require('path');
const os = require('os');

class IPCHandlers {
  constructor(windowManager, storeManager) {
    this.windowManager = windowManager;
    this.storeManager = storeManager;
    this.setupHandlers();
  }

  /**
   * Setup all IPC handlers
   */
  setupHandlers() {
    this.setupStoreHandlers();
    this.setupAppHandlers();
    this.setupFileHandlers();
    this.setupDialogHandlers();
    this.setupDownloadHandlers();
  }

  /**
   * Setup store-related IPC handlers
   */
  setupStoreHandlers() {
    ipcMain.handle('store-get', (event, key) => {
      return this.storeManager.get(key);
    });

    ipcMain.handle('store-set', (event, key, value) => {
      this.storeManager.set(key, value);
    });

    ipcMain.handle('store-delete', (event, key) => {
      this.storeManager.delete(key);
    });

    ipcMain.handle('store-clear', () => {
      this.storeManager.clear();
    });
  }

  /**
   * Setup app-related IPC handlers
   */
  setupAppHandlers() {
    ipcMain.handle('get-app-version', () => {
      return app.getVersion();
    });

    ipcMain.handle('get-app-name', () => {
      return app.getName();
    });

    ipcMain.handle('get-app-path', (event, name) => {
      return app.getPath(name);
    });

    ipcMain.handle('quit-app', () => {
      app.quit();
    });

    ipcMain.handle('restart-app', () => {
      app.relaunch();
      app.exit();
    });
  }

  /**
   * Setup file-related IPC handlers
   */
  setupFileHandlers() {
    ipcMain.handle('write-file', async (event, filePath, content) => {
      try {
        // Ensure directory exists
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        
        fs.writeFileSync(filePath, content, 'utf8');
        return { success: true };
      } catch (error) {
        console.error('Error writing file:', error);
        throw error;
      }
    });

    ipcMain.handle('read-file', async (event, filePath) => {
      try {
        if (!fs.existsSync(filePath)) {
          throw new Error(`File not found: ${filePath}`);
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        return content;
      } catch (error) {
        console.error('Error reading file:', error);
        throw error;
      }
    });

    ipcMain.handle('file-exists', (event, filePath) => {
      return fs.existsSync(filePath);
    });

    ipcMain.handle('delete-file', async (event, filePath) => {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        return { success: true };
      } catch (error) {
        console.error('Error deleting file:', error);
        throw error;
      }
    });

    ipcMain.handle('get-file-stats', async (event, filePath) => {
      try {
        if (!fs.existsSync(filePath)) {
          return null;
        }
        return fs.statSync(filePath);
      } catch (error) {
        console.error('Error getting file stats:', error);
        throw error;
      }
    });
  }

  /**
   * Setup dialog-related IPC handlers
   */
  setupDialogHandlers() {
    ipcMain.handle('show-message-box', async (event, options) => {
      const result = await dialog.showMessageBox(this.windowManager.getMainWindow(), options);
      return result;
    });

    ipcMain.handle('show-save-dialog', async (event, options) => {
      const result = await dialog.showSaveDialog(this.windowManager.getMainWindow(), options);
      return result;
    });

    ipcMain.handle('show-open-dialog', async (event, options) => {
      const result = await dialog.showOpenDialog(this.windowManager.getMainWindow(), options);
      return result;
    });

    ipcMain.handle('show-error-box', (event, title, content) => {
      dialog.showErrorBox(title, content);
    });
  }

  /**
   * Setup download-related IPC handlers
   */
  setupDownloadHandlers() {
    ipcMain.handle('open-downloads-folder', async () => {
      const downloadsPath = path.join(os.homedir(), 'Downloads');
      shell.openPath(downloadsPath);
    });

    ipcMain.handle('get-downloads-path', () => {
      return path.join(os.homedir(), 'Downloads');
    });

    ipcMain.handle('open-file', async (event, filePath) => {
      try {
        await shell.openPath(filePath);
        return { success: true };
      } catch (error) {
        console.error('Error opening file:', error);
        throw error;
      }
    });

    ipcMain.handle('show-item-in-folder', (event, filePath) => {
      shell.showItemInFolder(filePath);
    });

    ipcMain.handle('open-external', async (event, url) => {
      try {
        await shell.openExternal(url);
        return { success: true };
      } catch (error) {
        console.error('Error opening external URL:', error);
        throw error;
      }
    });
  }

  /**
   * Setup window-related IPC handlers
   */
  setupWindowHandlers() {
    ipcMain.handle('window-minimize', () => {
      this.windowManager.minimizeMainWindow();
    });

    ipcMain.handle('window-maximize', () => {
      this.windowManager.toggleMaximizeMainWindow();
    });

    ipcMain.handle('window-close', () => {
      this.windowManager.closeMainWindow();
    });

    ipcMain.handle('window-focus', () => {
      this.windowManager.focusMainWindow();
    });

    ipcMain.handle('window-get-bounds', () => {
      return this.windowManager.getMainWindowBounds();
    });

    ipcMain.handle('window-set-bounds', (event, bounds) => {
      this.windowManager.setMainWindowBounds(bounds);
    });

    ipcMain.handle('window-set-always-on-top', (event, flag) => {
      this.windowManager.setAlwaysOnTop(flag);
    });

    ipcMain.handle('window-toggle-devtools', () => {
      this.windowManager.toggleDevTools();
    });

    ipcMain.handle('window-reload', () => {
      this.windowManager.reloadMainWindow();
    });
  }

  /**
   * Setup system-related IPC handlers
   */
  setupSystemHandlers() {
    ipcMain.handle('get-system-info', () => {
      return {
        platform: process.platform,
        arch: process.arch,
        version: process.version,
        osType: os.type(),
        osRelease: os.release(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpus: os.cpus().length,
        homedir: os.homedir(),
        tmpdir: os.tmpdir()
      };
    });

    ipcMain.handle('get-environment-variable', (event, name) => {
      return process.env[name];
    });
  }

  /**
   * Remove all IPC handlers
   */
  removeAllHandlers() {
    const channels = [
      'store-get', 'store-set', 'store-delete', 'store-clear',
      'get-app-version', 'get-app-name', 'get-app-path', 'quit-app', 'restart-app',
      'write-file', 'read-file', 'file-exists', 'delete-file', 'get-file-stats',
      'show-message-box', 'show-save-dialog', 'show-open-dialog', 'show-error-box',
      'open-downloads-folder', 'get-downloads-path', 'open-file', 'show-item-in-folder', 'open-external',
      'window-minimize', 'window-maximize', 'window-close', 'window-focus',
      'window-get-bounds', 'window-set-bounds', 'window-set-always-on-top',
      'window-toggle-devtools', 'window-reload',
      'get-system-info', 'get-environment-variable'
    ];

    channels.forEach(channel => {
      ipcMain.removeAllListeners(channel);
    });
  }

  /**
   * Log IPC activity for debugging
   * @param {string} channel - IPC channel
   * @param {any} data - Data being sent
   */
  logIPCActivity(channel, data) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[IPC] ${channel}:`, data);
    }
  }
}

module.exports = IPCHandlers;
