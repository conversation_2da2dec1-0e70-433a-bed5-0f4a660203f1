/**
 * History entry model class
 */

import { generateId, formatUrl, extractDomain, getFaviconUrl } from '../../shared/utils.js';

export class HistoryEntry {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.url = data.url || '';
    this.title = data.title || '';
    this.favicon = data.favicon || null;
    this.visitedAt = data.visitedAt || new Date().toISOString();
    this.visitCount = data.visitCount || 1;
    this.websiteId = data.websiteId || null;
    this.tabId = data.tabId || null;
  }

  /**
   * Update visit information
   */
  recordVisit() {
    this.visitCount++;
    this.visitedAt = new Date().toISOString();
  }

  /**
   * Update title and favicon
   * @param {string} title - Page title
   * @param {string} favicon - Favicon URL
   */
  updateMetadata(title, favicon = null) {
    if (title) {
      this.title = title;
    }
    
    if (favicon) {
      this.favicon = favicon;
    } else if (!this.favicon) {
      this.favicon = getFaviconUrl(this.url);
    }
  }

  /**
   * Get domain name
   * @returns {string} Domain name
   */
  getDomain() {
    return extractDomain(this.url);
  }

  /**
   * Get display title
   * @returns {string} Display title
   */
  getDisplayTitle() {
    return this.title || this.getDomain() || this.url;
  }

  /**
   * Check if entry matches search query
   * @param {string} query - Search query
   * @returns {boolean} True if matches
   */
  matchesSearch(query) {
    if (!query || query.trim().length === 0) return true;
    
    const searchTerm = query.toLowerCase().trim();
    const searchableText = [
      this.title,
      this.url,
      this.getDomain()
    ].join(' ').toLowerCase();
    
    return searchableText.includes(searchTerm);
  }

  /**
   * Get history entry data for serialization
   * @returns {Object} Serializable data
   */
  toJSON() {
    return {
      id: this.id,
      url: this.url,
      title: this.title,
      favicon: this.favicon,
      visitedAt: this.visitedAt,
      visitCount: this.visitCount,
      websiteId: this.websiteId,
      tabId: this.tabId
    };
  }

  /**
   * Create HistoryEntry instance from JSON data
   * @param {Object} data - JSON data
   * @returns {HistoryEntry} HistoryEntry instance
   */
  static fromJSON(data) {
    return new HistoryEntry(data);
  }

  /**
   * Create history entry from navigation
   * @param {string} url - URL visited
   * @param {string} title - Page title
   * @param {string} websiteId - Associated website ID
   * @param {string} tabId - Associated tab ID
   * @returns {HistoryEntry} New history entry
   */
  static fromNavigation(url, title = '', websiteId = null, tabId = null) {
    return new HistoryEntry({
      url: formatUrl(url),
      title,
      websiteId,
      tabId
    });
  }

  /**
   * Group history entries by date
   * @param {HistoryEntry[]} entries - History entries
   * @returns {Object} Grouped entries by date
   */
  static groupByDate(entries) {
    const groups = {};
    
    entries.forEach(entry => {
      const date = new Date(entry.visitedAt);
      const dateKey = date.toDateString();
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      
      groups[dateKey].push(entry);
    });
    
    // Sort entries within each group by visit time (newest first)
    Object.keys(groups).forEach(dateKey => {
      groups[dateKey].sort((a, b) => new Date(b.visitedAt) - new Date(a.visitedAt));
    });
    
    return groups;
  }

  /**
   * Get most visited entries
   * @param {HistoryEntry[]} entries - History entries
   * @param {number} limit - Number of entries to return
   * @returns {HistoryEntry[]} Most visited entries
   */
  static getMostVisited(entries, limit = 10) {
    return entries
      .sort((a, b) => b.visitCount - a.visitCount)
      .slice(0, limit);
  }

  /**
   * Get recent entries
   * @param {HistoryEntry[]} entries - History entries
   * @param {number} limit - Number of entries to return
   * @returns {HistoryEntry[]} Recent entries
   */
  static getRecent(entries, limit = 10) {
    return entries
      .sort((a, b) => new Date(b.visitedAt) - new Date(a.visitedAt))
      .slice(0, limit);
  }
}
