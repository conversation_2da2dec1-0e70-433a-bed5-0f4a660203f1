/**
 * Main process entry point - Refactored modular architecture
 */

const { app, BrowserWindow, protocol } = require('electron');
const path = require('path');

// Import our modular components
const WindowManager = require('./window-manager');
const MenuManager = require('./menu-manager');
const IPCHandlers = require('./ipc-handlers');
const StoreManager = require('./store-manager');
const DownloadManager = require('./download-manager');
const SessionManager = require('./session-manager');

class UnrestrictedBrowserApp {
  constructor() {
    this.windowManager = null;
    this.menuManager = null;
    this.ipcHandlers = null;
    this.storeManager = null;
    this.downloadManager = null;
    this.sessionManager = null;
    this.isReady = false;
  }

  /**
   * Initialize the application
   */
  async initialize() {
    try {
      console.log('Initializing Unrestricted Browser...');

      // Initialize core managers
      this.storeManager = new StoreManager();
      this.sessionManager = new SessionManager();
      this.windowManager = new WindowManager();
      this.menuManager = new MenuManager(this.windowManager);
      this.downloadManager = new DownloadManager(this.windowManager);
      this.ipcHandlers = new IPCHandlers(this.windowManager, this.storeManager);

      // Create the main window
      this.windowManager.createMainWindow();

      // Create the application menu
      this.menuManager.createMenu();

      this.isReady = true;
      console.log('Unrestricted Browser initialized successfully');

    } catch (error) {
      console.error('Error initializing application:', error);
      this.handleInitializationError(error);
    }
  }

  /**
   * Handle initialization errors
   * @param {Error} error - Initialization error
   */
  handleInitializationError(error) {
    console.error('Failed to initialize application:', error);
    
    // Show error dialog if possible
    if (this.windowManager && this.windowManager.isMainWindowAvailable()) {
      const { dialog } = require('electron');
      dialog.showErrorBox('Initialization Error', 
        `Failed to start Unrestricted Browser:\n\n${error.message}`);
    }
    
    // Exit the application
    app.quit();
  }

  /**
   * Handle application activation (macOS)
   */
  handleActivation() {
    if (BrowserWindow.getAllWindows().length === 0) {
      this.windowManager.createMainWindow();
    }
  }

  /**
   * Handle before quit event
   * @param {Event} event - Quit event
   */
  async handleBeforeQuit(event) {
    if (!this.isReady) return;

    try {
      // Save any pending data
      console.log('Saving application state before quit...');
      
      // Cleanup managers
      await this.cleanup();
      
      console.log('Application cleanup completed');
    } catch (error) {
      console.error('Error during application cleanup:', error);
    }
  }

  /**
   * Cleanup application resources
   */
  async cleanup() {
    try {
      if (this.downloadManager) {
        this.downloadManager.cleanup();
      }

      if (this.sessionManager) {
        this.sessionManager.cleanup();
      }

      if (this.ipcHandlers) {
        this.ipcHandlers.removeAllHandlers();
      }

      if (this.windowManager) {
        this.windowManager.cleanup();
      }

      console.log('All managers cleaned up successfully');
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  /**
   * Get application information
   * @returns {Object} Application info
   */
  getAppInfo() {
    return {
      name: app.getName(),
      version: app.getVersion(),
      isReady: this.isReady,
      platform: process.platform,
      arch: process.arch,
      electronVersion: process.versions.electron,
      nodeVersion: process.versions.node,
      chromeVersion: process.versions.chrome
    };
  }

  /**
   * Restart the application
   */
  restart() {
    app.relaunch();
    app.exit();
  }

  /**
   * Show application in dock/taskbar (if hidden)
   */
  show() {
    if (process.platform === 'darwin') {
      app.dock.show();
    }
    this.windowManager.focusMainWindow();
  }

  /**
   * Hide application from dock/taskbar
   */
  hide() {
    if (process.platform === 'darwin') {
      app.dock.hide();
    }
    this.windowManager.minimizeMainWindow();
  }
}

// Register protocol schemes before app ready
protocol.registerSchemesAsPrivileged([
  { scheme: 'app', privileges: { secure: true, standard: true } }
]);

// Create application instance
const browserApp = new UnrestrictedBrowserApp();

// App event handlers
app.whenReady().then(() => {
  browserApp.initialize();
  
  app.on('activate', () => {
    browserApp.handleActivation();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', (event) => {
  browserApp.handleBeforeQuit(event);
});

// Handle second instance (single instance application)
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // Someone tried to run a second instance, focus our window instead
    browserApp.show();
  });
}

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  // For unrestricted browsing, accept all certificates
  event.preventDefault();
  callback(true);
});

// Handle login requests
app.on('login', (event, webContents, request, authInfo, callback) => {
  // Could implement automatic login handling here
  console.log('Login request for:', authInfo);
});

// Export for testing purposes
module.exports = browserApp;
