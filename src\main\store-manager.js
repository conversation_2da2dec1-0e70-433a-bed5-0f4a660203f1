/**
 * Store management module
 */

const path = require('path');
const fs = require('fs');
const os = require('os');

class StoreManager {
  constructor() {
    this.store = null;
    this.fallbackStore = null;
    this.initializeStore();
  }

  /**
   * Initialize the store (electron-store or fallback)
   */
  initializeStore() {
    try {
      const Store = require('electron-store');
      this.store = new Store();
      console.log('Using electron-store for data persistence');
    } catch (error) {
      console.log('electron-store not available, using fallback storage');
      this.initializeFallbackStore();
    }
  }

  /**
   * Initialize fallback file-based store
   */
  initializeFallbackStore() {
    this.fallbackStore = new FallbackStore();
    this.store = this.fallbackStore;
  }

  /**
   * Get value from store
   * @param {string} key - Storage key
   * @returns {any} Stored value
   */
  get(key) {
    try {
      return this.store.get(key);
    } catch (error) {
      console.error('Error getting value from store:', error);
      return undefined;
    }
  }

  /**
   * Set value in store
   * @param {string} key - Storage key
   * @param {any} value - Value to store
   */
  set(key, value) {
    try {
      this.store.set(key, value);
    } catch (error) {
      console.error('Error setting value in store:', error);
    }
  }

  /**
   * Delete key from store
   * @param {string} key - Storage key
   */
  delete(key) {
    try {
      this.store.delete(key);
    } catch (error) {
      console.error('Error deleting key from store:', error);
    }
  }

  /**
   * Clear all data from store
   */
  clear() {
    try {
      this.store.clear();
    } catch (error) {
      console.error('Error clearing store:', error);
    }
  }

  /**
   * Check if key exists in store
   * @param {string} key - Storage key
   * @returns {boolean} True if key exists
   */
  has(key) {
    try {
      return this.store.has ? this.store.has(key) : this.store.get(key) !== undefined;
    } catch (error) {
      console.error('Error checking key existence:', error);
      return false;
    }
  }

  /**
   * Get all keys from store
   * @returns {string[]} Array of keys
   */
  getKeys() {
    try {
      if (this.store.keys) {
        return this.store.keys();
      } else if (this.fallbackStore) {
        return Object.keys(this.fallbackStore.data);
      }
      return [];
    } catch (error) {
      console.error('Error getting keys from store:', error);
      return [];
    }
  }

  /**
   * Get store size
   * @returns {number} Number of items in store
   */
  size() {
    try {
      if (this.store.size !== undefined) {
        return this.store.size;
      } else if (this.fallbackStore) {
        return Object.keys(this.fallbackStore.data).length;
      }
      return 0;
    } catch (error) {
      console.error('Error getting store size:', error);
      return 0;
    }
  }

  /**
   * Export store data
   * @returns {Object} Store data
   */
  exportData() {
    try {
      if (this.fallbackStore) {
        return { ...this.fallbackStore.data };
      } else {
        const data = {};
        const keys = this.getKeys();
        keys.forEach(key => {
          data[key] = this.get(key);
        });
        return data;
      }
    } catch (error) {
      console.error('Error exporting store data:', error);
      return {};
    }
  }

  /**
   * Import store data
   * @param {Object} data - Data to import
   */
  importData(data) {
    try {
      Object.keys(data).forEach(key => {
        this.set(key, data[key]);
      });
    } catch (error) {
      console.error('Error importing store data:', error);
    }
  }

  /**
   * Get store file path (for fallback store)
   * @returns {string|null} Store file path
   */
  getStorePath() {
    if (this.fallbackStore) {
      return this.fallbackStore.storePath;
    }
    return null;
  }
}

/**
 * Fallback store implementation using filesystem
 */
class FallbackStore {
  constructor() {
    this.storePath = path.join(os.homedir(), '.unrestricted-browser-data.json');
    this.data = this.loadData();
  }

  /**
   * Load data from file
   * @returns {Object} Loaded data
   */
  loadData() {
    try {
      if (fs.existsSync(this.storePath)) {
        const content = fs.readFileSync(this.storePath, 'utf8');
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Error loading fallback data:', error);
    }
    return {};
  }

  /**
   * Save data to file
   */
  saveData() {
    try {
      fs.writeFileSync(this.storePath, JSON.stringify(this.data, null, 2), 'utf8');
    } catch (error) {
      console.error('Error saving fallback data:', error);
    }
  }

  /**
   * Get value
   * @param {string} key - Storage key
   * @returns {any} Stored value
   */
  get(key) {
    return this.data[key];
  }

  /**
   * Set value
   * @param {string} key - Storage key
   * @param {any} value - Value to store
   */
  set(key, value) {
    this.data[key] = value;
    this.saveData();
  }

  /**
   * Delete key
   * @param {string} key - Storage key
   */
  delete(key) {
    delete this.data[key];
    this.saveData();
  }

  /**
   * Clear all data
   */
  clear() {
    this.data = {};
    this.saveData();
  }

  /**
   * Check if key exists
   * @param {string} key - Storage key
   * @returns {boolean} True if key exists
   */
  has(key) {
    return this.data.hasOwnProperty(key);
  }
}

module.exports = StoreManager;
