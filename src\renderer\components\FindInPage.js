/**
 * Find in page component for searching within web pages
 */

export class FindInPage {
  constructor(eventService, webviewManager) {
    this.eventService = eventService;
    this.webviewManager = webviewManager;
    
    // Get DOM elements
    this.findBar = document.getElementById('findBar');
    this.findInput = document.getElementById('findInput');
    this.findPrevBtn = document.getElementById('findPrevBtn');
    this.findNextBtn = document.getElementById('findNextBtn');
    this.findResults = document.getElementById('findResults');
    this.closeFindBtn = document.getElementById('closeFindBtn');
    
    this.currentQuery = '';
    this.currentResults = { activeMatchOrdinal: 0, matches: 0 };
    this.isVisible = false;
    
    this.setupEventListeners();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Find input
    if (this.findInput) {
      this.findInput.addEventListener('input', (e) => {
        this.handleInputChange(e);
      });

      this.findInput.addEventListener('keydown', (e) => {
        this.handleInputKeydown(e);
      });
    }

    // Navigation buttons
    if (this.findPrevBtn) {
      this.findPrevBtn.addEventListener('click', () => {
        this.findPrevious();
      });
    }

    if (this.findNextBtn) {
      this.findNextBtn.addEventListener('click', () => {
        this.findNext();
      });
    }

    // Close button
    if (this.closeFindBtn) {
      this.closeFindBtn.addEventListener('click', () => {
        this.hide();
      });
    }

    // Event service listeners
    this.eventService.on('show-find', () => {
      this.show();
    });

    this.eventService.on('hide-find', () => {
      this.hide();
    });

    this.eventService.on('find-in-page', (query) => {
      this.search(query);
    });

    // Global keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        this.show();
      } else if (e.key === 'Escape' && this.isVisible) {
        e.preventDefault();
        this.hide();
      } else if (this.isVisible) {
        if (e.key === 'F3' || ((e.ctrlKey || e.metaKey) && e.key === 'g')) {
          e.preventDefault();
          if (e.shiftKey) {
            this.findPrevious();
          } else {
            this.findNext();
          }
        }
      }
    });

    // Listen for webview find results
    this.setupWebviewListeners();
  }

  /**
   * Setup webview event listeners for find results
   */
  setupWebviewListeners() {
    // We'll need to listen for find results from the active webview
    this.eventService.on('tab-switched', () => {
      this.attachToActiveWebview();
    });
  }

  /**
   * Attach find listeners to active webview
   */
  attachToActiveWebview() {
    const webview = this.webviewManager.getActiveWebview();
    if (webview) {
      // Remove previous listeners
      webview.removeAllListeners?.('found-in-page');
      
      // Add new listener
      webview.addEventListener('found-in-page', (event) => {
        this.handleFindResults(event.result);
      });
    }
  }

  /**
   * Handle input change
   * @param {Event} event - Input event
   */
  handleInputChange(event) {
    const query = event.target.value;
    this.search(query);
  }

  /**
   * Handle input keydown
   * @param {KeyboardEvent} event - Keydown event
   */
  handleInputKeydown(event) {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        if (event.shiftKey) {
          this.findPrevious();
        } else {
          this.findNext();
        }
        break;
      case 'Escape':
        event.preventDefault();
        this.hide();
        break;
    }
  }

  /**
   * Show find bar
   */
  show() {
    if (this.findBar) {
      this.findBar.style.display = 'flex';
      this.isVisible = true;
      
      // Focus input and select text
      if (this.findInput) {
        this.findInput.focus();
        this.findInput.select();
      }
      
      // Attach to current webview
      this.attachToActiveWebview();
      
      // If there's existing text, search for it
      if (this.findInput && this.findInput.value) {
        this.search(this.findInput.value);
      }
    }
  }

  /**
   * Hide find bar
   */
  hide() {
    if (this.findBar) {
      this.findBar.style.display = 'none';
      this.isVisible = false;
      
      // Stop finding in webview
      this.stopFind();
      
      // Clear results
      this.updateResults({ activeMatchOrdinal: 0, matches: 0 });
    }
  }

  /**
   * Search for text
   * @param {string} query - Search query
   */
  search(query) {
    this.currentQuery = query;
    
    if (!query || query.trim().length === 0) {
      this.stopFind();
      this.updateResults({ activeMatchOrdinal: 0, matches: 0 });
      return;
    }

    const webview = this.webviewManager.getActiveWebview();
    if (webview) {
      webview.findInPage(query, {
        forward: true,
        findNext: false,
        matchCase: false
      });
    }
  }

  /**
   * Find next occurrence
   */
  findNext() {
    if (!this.currentQuery) return;

    const webview = this.webviewManager.getActiveWebview();
    if (webview) {
      webview.findInPage(this.currentQuery, {
        forward: true,
        findNext: true,
        matchCase: false
      });
    }
  }

  /**
   * Find previous occurrence
   */
  findPrevious() {
    if (!this.currentQuery) return;

    const webview = this.webviewManager.getActiveWebview();
    if (webview) {
      webview.findInPage(this.currentQuery, {
        forward: false,
        findNext: true,
        matchCase: false
      });
    }
  }

  /**
   * Stop finding
   */
  stopFind() {
    const webview = this.webviewManager.getActiveWebview();
    if (webview) {
      webview.stopFindInPage('clearSelection');
    }
  }

  /**
   * Handle find results from webview
   * @param {Object} result - Find result object
   */
  handleFindResults(result) {
    this.currentResults = result;
    this.updateResults(result);
    this.updateButtonStates(result);
  }

  /**
   * Update results display
   * @param {Object} result - Find result object
   */
  updateResults(result) {
    if (this.findResults) {
      if (result.matches === 0) {
        this.findResults.textContent = this.currentQuery ? 'No results' : '';
        this.findResults.className = 'find-results no-results';
      } else {
        this.findResults.textContent = `${result.activeMatchOrdinal}/${result.matches}`;
        this.findResults.className = 'find-results';
      }
    }
  }

  /**
   * Update button states
   * @param {Object} result - Find result object
   */
  updateButtonStates(result) {
    const hasResults = result.matches > 0;
    
    if (this.findPrevBtn) {
      this.findPrevBtn.disabled = !hasResults;
    }
    
    if (this.findNextBtn) {
      this.findNextBtn.disabled = !hasResults;
    }
  }

  /**
   * Set search query
   * @param {string} query - Search query
   */
  setQuery(query) {
    if (this.findInput) {
      this.findInput.value = query;
      this.search(query);
    }
  }

  /**
   * Get current search query
   * @returns {string} Current query
   */
  getQuery() {
    return this.currentQuery;
  }

  /**
   * Check if find bar is visible
   * @returns {boolean} True if visible
   */
  isShown() {
    return this.isVisible;
  }

  /**
   * Toggle find bar visibility
   */
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * Clear search
   */
  clear() {
    if (this.findInput) {
      this.findInput.value = '';
    }
    this.currentQuery = '';
    this.stopFind();
    this.updateResults({ activeMatchOrdinal: 0, matches: 0 });
  }

  /**
   * Focus find input
   */
  focus() {
    if (this.findInput && this.isVisible) {
      this.findInput.focus();
    }
  }

  /**
   * Get find statistics
   * @returns {Object} Find statistics
   */
  getStats() {
    return {
      query: this.currentQuery,
      results: this.currentResults,
      isVisible: this.isVisible
    };
  }

  /**
   * Set find options
   * @param {Object} options - Find options
   */
  setOptions(options = {}) {
    this.options = {
      matchCase: false,
      wholeWord: false,
      ...options
    };
  }

  /**
   * Search with options
   * @param {string} query - Search query
   * @param {Object} options - Search options
   */
  searchWithOptions(query, options = {}) {
    this.currentQuery = query;
    
    if (!query || query.trim().length === 0) {
      this.stopFind();
      this.updateResults({ activeMatchOrdinal: 0, matches: 0 });
      return;
    }

    const webview = this.webviewManager.getActiveWebview();
    if (webview) {
      const findOptions = {
        forward: true,
        findNext: false,
        matchCase: options.matchCase || false,
        wordStart: options.wholeWord || false,
        medialCapitalAsWordStart: false,
        ...options
      };
      
      webview.findInPage(query, findOptions);
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.hide();
    this.currentQuery = '';
    this.currentResults = { activeMatchOrdinal: 0, matches: 0 };
  }
}
