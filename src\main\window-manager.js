/**
 * Window management module
 */

const { BrowserWindow, shell } = require('electron');
const path = require('path');

class WindowManager {
  constructor() {
    this.mainWindow = null;
    this.windowConfig = {
      width: 1400,
      height: 900,
      minWidth: 1000,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, '../preload.js'),
        allowRunningInsecureContent: true,
        webviewTag: true,
        webSecurity: false,
        experimentalFeatures: true
      },
      titleBarStyle: 'default',
      icon: path.join(__dirname, '../../assets/icon.png'),
      show: false,
      backgroundColor: '#1a1a1a'
    };
  }

  /**
   * Create the main application window
   * @param {Object} options - Additional window options
   * @returns {BrowserWindow} Created window
   */
  createMainWindow(options = {}) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.focus();
      return this.mainWindow;
    }

    const config = { ...this.windowConfig, ...options };
    this.mainWindow = new BrowserWindow(config);

    // Load the app
    this.mainWindow.loadFile(path.join(__dirname, '../index.html'));

    // Setup window event handlers
    this.setupWindowEvents();

    return this.mainWindow;
  }

  /**
   * Setup window event handlers
   */
  setupWindowEvents() {
    if (!this.mainWindow) return;

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      this.mainWindow.webContents.send('app-ready');

      // Open DevTools in development
      if (process.argv.includes('--dev')) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // Handle window state changes
    this.mainWindow.on('maximize', () => {
      this.mainWindow.webContents.send('window-state-changed', 'maximized');
    });

    this.mainWindow.on('unmaximize', () => {
      this.mainWindow.webContents.send('window-state-changed', 'normal');
    });

    this.mainWindow.on('minimize', () => {
      this.mainWindow.webContents.send('window-state-changed', 'minimized');
    });

    this.mainWindow.on('restore', () => {
      this.mainWindow.webContents.send('window-state-changed', 'normal');
    });

    // Handle focus events
    this.mainWindow.on('focus', () => {
      this.mainWindow.webContents.send('window-focus-changed', true);
    });

    this.mainWindow.on('blur', () => {
      this.mainWindow.webContents.send('window-focus-changed', false);
    });
  }

  /**
   * Get the main window instance
   * @returns {BrowserWindow|null} Main window or null
   */
  getMainWindow() {
    return this.mainWindow;
  }

  /**
   * Check if main window exists and is not destroyed
   * @returns {boolean} True if window is available
   */
  isMainWindowAvailable() {
    return this.mainWindow && !this.mainWindow.isDestroyed();
  }

  /**
   * Send message to main window
   * @param {string} channel - IPC channel
   * @param {...any} args - Arguments to send
   */
  sendToMainWindow(channel, ...args) {
    if (this.isMainWindowAvailable()) {
      this.mainWindow.webContents.send(channel, ...args);
    }
  }

  /**
   * Focus the main window
   */
  focusMainWindow() {
    if (this.isMainWindowAvailable()) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.focus();
    }
  }

  /**
   * Close the main window
   */
  closeMainWindow() {
    if (this.isMainWindowAvailable()) {
      this.mainWindow.close();
    }
  }

  /**
   * Minimize the main window
   */
  minimizeMainWindow() {
    if (this.isMainWindowAvailable()) {
      this.mainWindow.minimize();
    }
  }

  /**
   * Maximize or restore the main window
   */
  toggleMaximizeMainWindow() {
    if (this.isMainWindowAvailable()) {
      if (this.mainWindow.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow.maximize();
      }
    }
  }

  /**
   * Set window bounds
   * @param {Object} bounds - Window bounds
   */
  setMainWindowBounds(bounds) {
    if (this.isMainWindowAvailable()) {
      this.mainWindow.setBounds(bounds);
    }
  }

  /**
   * Get window bounds
   * @returns {Object|null} Window bounds or null
   */
  getMainWindowBounds() {
    if (this.isMainWindowAvailable()) {
      return this.mainWindow.getBounds();
    }
    return null;
  }

  /**
   * Set window always on top
   * @param {boolean} flag - Always on top flag
   */
  setAlwaysOnTop(flag) {
    if (this.isMainWindowAvailable()) {
      this.mainWindow.setAlwaysOnTop(flag);
    }
  }

  /**
   * Toggle DevTools
   */
  toggleDevTools() {
    if (this.isMainWindowAvailable()) {
      this.mainWindow.webContents.toggleDevTools();
    }
  }

  /**
   * Reload the main window
   */
  reloadMainWindow() {
    if (this.isMainWindowAvailable()) {
      this.mainWindow.reload();
    }
  }

  /**
   * Get all windows
   * @returns {BrowserWindow[]} Array of all windows
   */
  getAllWindows() {
    return BrowserWindow.getAllWindows();
  }

  /**
   * Close all windows
   */
  closeAllWindows() {
    BrowserWindow.getAllWindows().forEach(window => {
      window.close();
    });
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.removeAllListeners();
    }
    this.mainWindow = null;
  }
}

module.exports = WindowManager;
