/**
 * Renderer process entry point - Refactored modular architecture
 */

// Import services
import { StorageService } from './services/StorageService.js';
import { EventService } from './services/EventService.js';
import { DataService } from './services/DataService.js';

// Import components
import { WebviewManager } from './components/WebviewManager.js';
import { TabManager } from './components/TabManager.js';
import { WebsiteManager } from './components/WebsiteManager.js';
import { NavigationBar } from './components/NavigationBar.js';
import { FindInPage } from './components/FindInPage.js';
import { ZoomController } from './components/ZoomController.js';

// Import utilities
import { logInfo, logError } from '../shared/utils.js';
import { DEFAULT_SETTINGS } from '../shared/constants.js';

class UnrestrictedBrowserRenderer {
  constructor() {
    this.services = {};
    this.components = {};
    this.isInitialized = false;
  }

  /**
   * Initialize the renderer application
   */
  async initialize() {
    try {
      logInfo('Renderer', 'Initializing Unrestricted Browser renderer...');

      // Initialize services
      await this.initializeServices();

      // Initialize components
      await this.initializeComponents();

      // Setup global event handlers
      this.setupGlobalEventHandlers();

      // Load initial data
      await this.loadInitialData();

      // Setup UI
      this.setupUI();

      this.isInitialized = true;
      logInfo('Renderer', 'Renderer initialization completed successfully');

    } catch (error) {
      logError('Renderer', `Initialization failed: ${error.message}`);
      this.handleInitializationError(error);
    }
  }

  /**
   * Initialize core services
   */
  async initializeServices() {
    logInfo('Renderer', 'Initializing services...');

    // Event service (first, as others depend on it)
    this.services.eventService = new EventService();

    // Storage service
    this.services.storageService = new StorageService();

    // Data service
    this.services.dataService = new DataService(
      this.services.storageService,
      this.services.eventService
    );

    logInfo('Renderer', 'Services initialized successfully');
  }

  /**
   * Initialize UI components
   */
  async initializeComponents() {
    logInfo('Renderer', 'Initializing components...');

    // Webview manager (first, as others depend on it)
    this.components.webviewManager = new WebviewManager(
      this.services.dataService,
      this.services.eventService
    );

    // Tab manager
    this.components.tabManager = new TabManager(
      this.services.dataService,
      this.services.eventService,
      this.components.webviewManager
    );

    // Website manager
    this.components.websiteManager = new WebsiteManager(
      this.services.dataService,
      this.services.eventService,
      this.components.tabManager
    );

    // Navigation bar
    this.components.navigationBar = new NavigationBar(
      this.services.dataService,
      this.services.eventService,
      this.components.webviewManager
    );

    // Find in page
    this.components.findInPage = new FindInPage(
      this.services.eventService,
      this.components.webviewManager
    );

    // Zoom controller
    this.components.zoomController = new ZoomController(
      this.services.eventService,
      this.components.webviewManager,
      this.services.dataService
    );

    logInfo('Renderer', 'Components initialized successfully');
  }

  /**
   * Setup global event handlers
   */
  setupGlobalEventHandlers() {
    // App ready event
    this.services.eventService.on('app-ready', () => {
      this.onAppReady();
    });

    // Data events
    this.services.eventService.on('data-loaded', () => {
      this.onDataLoaded();
    });

    // Error handling
    window.addEventListener('error', (event) => {
      logError('Renderer', `Global error: ${event.error.message}`);
    });

    window.addEventListener('unhandledrejection', (event) => {
      logError('Renderer', `Unhandled promise rejection: ${event.reason}`);
    });

    // Zoom events
    this.services.eventService.on('zoom-in', () => {
      this.zoomIn();
    });

    this.services.eventService.on('zoom-out', () => {
      this.zoomOut();
    });

    this.services.eventService.on('reset-zoom', () => {
      this.resetZoom();
    });

    // Reload current site
    this.services.eventService.on('reload-current-site', () => {
      this.components.tabManager.reloadActiveTab();
    });

    // Clear all data
    this.services.eventService.on('clear-all-data', () => {
      this.clearAllData();
    });

    // Settings
    this.services.eventService.on('show-settings', () => {
      this.showSettings();
    });

    // History
    this.services.eventService.on('show-history', () => {
      this.showHistory();
    });

    // Downloads
    this.services.eventService.on('show-downloads', () => {
      this.showDownloads();
    });

    // Find
    this.services.eventService.on('show-find', () => {
      this.showFind();
    });

    // Dev tools
    this.services.eventService.on('toggle-dev-tools', () => {
      this.toggleDevTools();
    });
  }

  /**
   * Load initial data
   */
  async loadInitialData() {
    logInfo('Renderer', 'Loading initial data...');
    await this.services.dataService.initialize();
  }

  /**
   * Setup initial UI state
   */
  setupUI() {
    // Render website list
    this.components.websiteManager.renderWebsiteList();
    this.components.websiteManager.updateStats();

    // Create initial tab if no websites or if auto-load is disabled
    if (this.services.dataService.websites.length === 0) {
      this.showWelcomeScreen();
    } else if (this.services.dataService.settings.autoLoadLastWebsite) {
      this.loadLastWebsite();
    } else {
      this.components.tabManager.createNewTab();
    }

    // Navigation is now handled by NavigationBar component

    // Setup modals
    this.setupModals();
  }



  /**
   * Setup modal dialogs
   */
  setupModals() {
    // Add website modal
    this.setupAddWebsiteModal();
    
    // Settings modal
    this.setupSettingsModal();
    
    // History modal
    this.setupHistoryModal();
    
    // Downloads modal
    this.setupDownloadsModal();
  }

  /**
   * Setup add website modal
   */
  setupAddWebsiteModal() {
    const modal = document.getElementById('addWebsiteModal');
    const form = document.getElementById('addWebsiteForm');
    const closeBtn = document.getElementById('closeAddModal');
    const cancelBtn = document.getElementById('cancelAddBtn');

    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleAddWebsite();
      });
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideModal(modal);
      });
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        this.hideModal(modal);
      });
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideModal(modal);
        }
      });
    }
  }

  /**
   * Handle add website form submission
   */
  handleAddWebsite() {
    const nameInput = document.getElementById('websiteName');
    const urlInput = document.getElementById('websiteUrl');
    const categorySelect = document.getElementById('websiteCategory');
    const favoriteCheckbox = document.getElementById('websiteFavorite');

    if (!nameInput || !urlInput) return;

    try {
      const websiteData = {
        name: nameInput.value.trim(),
        url: urlInput.value.trim(),
        category: categorySelect ? categorySelect.value : 'general',
        isFavorite: favoriteCheckbox ? favoriteCheckbox.checked : false
      };

      this.services.dataService.addWebsite(websiteData);
      this.hideModal(document.getElementById('addWebsiteModal'));
      
      // Show success message
      this.showNotification('Website added successfully!', 'success');

    } catch (error) {
      this.showNotification(error.message, 'error');
    }
  }

  /**
   * Navigate to URL
   * @param {string} url - URL to navigate to
   */
  navigateToUrl(url) {
    this.components.webviewManager.navigateActiveWebview(url);
  }

  /**
   * Show welcome screen
   */
  showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
      welcomeScreen.style.display = 'flex';
    }
  }

  /**
   * Load last website
   */
  async loadLastWebsite() {
    try {
      const lastWebsiteId = await this.services.storageService.getLastWebsite();
      if (lastWebsiteId) {
        const website = this.services.dataService.getWebsiteById(lastWebsiteId);
        if (website) {
          this.components.websiteManager.openWebsite(website);
          return;
        }
      }
    } catch (error) {
      logError('Renderer', `Error loading last website: ${error.message}`);
    }
    
    // Fallback to new tab
    this.components.tabManager.createNewTab();
  }

  /**
   * Handle app ready event
   */
  onAppReady() {
    logInfo('Renderer', 'App ready event received');
  }

  /**
   * Handle data loaded event
   */
  onDataLoaded() {
    logInfo('Renderer', 'Data loaded event received');
    this.components.websiteManager.renderWebsiteList();
    this.components.websiteManager.updateStats();
  }

  /**
   * Handle initialization error
   * @param {Error} error - Initialization error
   */
  handleInitializationError(error) {
    // Show error message to user
    document.body.innerHTML = `
      <div class="error-screen">
        <h1>Initialization Error</h1>
        <p>Failed to initialize Unrestricted Browser:</p>
        <pre>${error.message}</pre>
        <button onclick="location.reload()">Retry</button>
      </div>
    `;
  }

  // Utility methods

  zoomIn() {
    this.components.zoomController.zoomIn();
  }

  zoomOut() {
    this.components.zoomController.zoomOut();
  }

  resetZoom() {
    this.components.zoomController.resetZoom();
  }

  async clearAllData() {
    const result = await window.electronAPI.showMessageBox({
      type: 'warning',
      buttons: ['Cancel', 'Clear All Data'],
      defaultId: 0,
      title: 'Clear All Data',
      message: 'Are you sure you want to clear all data? This action cannot be undone.',
      detail: 'This will remove all websites, history, downloads, and settings.'
    });

    if (result.response === 1) {
      await this.services.dataService.clearAllData();
      location.reload();
    }
  }

  showSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  showHistory() {
    const modal = document.getElementById('historyModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  showDownloads() {
    const modal = document.getElementById('downloadsModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  showFind() {
    this.components.findInPage.show();
  }

  toggleDevTools() {
    this.components.webviewManager.toggleDevTools();
  }

  hideModal(modal) {
    if (modal) {
      modal.style.display = 'none';
    }
  }

  showNotification(message, type = 'info') {
    // Simple notification implementation
    console.log(`[${type.toUpperCase()}] ${message}`);
  }

  setupSettingsModal() {
    // Implementation for settings modal
  }

  setupHistoryModal() {
    // Implementation for history modal
  }

  setupDownloadsModal() {
    // Implementation for downloads modal
  }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const app = new UnrestrictedBrowserRenderer();
  app.initialize();
  
  // Make app globally available for debugging
  window.browserApp = app;
});
