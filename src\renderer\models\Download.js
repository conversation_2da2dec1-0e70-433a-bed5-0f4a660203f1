/**
 * Download model class
 */

import { generateId, formatFileSize, sanitizeFilename } from '../../shared/utils.js';

export const DOWNLOAD_STATES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  PAUSED: 'paused',
  INTERRUPTED: 'interrupted'
};

export class Download {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.filename = data.filename || '';
    this.url = data.url || '';
    this.savePath = data.savePath || '';
    this.totalBytes = data.totalBytes || 0;
    this.receivedBytes = data.receivedBytes || 0;
    this.state = data.state || DOWNLOAD_STATES.PENDING;
    this.startedAt = data.startedAt || new Date().toISOString();
    this.completedAt = data.completedAt || null;
    this.error = data.error || null;
    this.mimeType = data.mimeType || '';
    this.websiteId = data.websiteId || null;
    this.tabId = data.tabId || null;
  }

  /**
   * Update download progress
   * @param {number} receivedBytes - Bytes received
   * @param {number} totalBytes - Total bytes (optional)
   */
  updateProgress(receivedBytes, totalBytes = null) {
    this.receivedBytes = receivedBytes;
    
    if (totalBytes !== null) {
      this.totalBytes = totalBytes;
    }
    
    this.state = DOWNLOAD_STATES.IN_PROGRESS;
  }

  /**
   * Mark download as completed
   * @param {string} savePath - Final save path
   */
  complete(savePath = null) {
    this.state = DOWNLOAD_STATES.COMPLETED;
    this.completedAt = new Date().toISOString();
    this.receivedBytes = this.totalBytes;
    
    if (savePath) {
      this.savePath = savePath;
    }
  }

  /**
   * Mark download as failed
   * @param {string} error - Error message
   */
  fail(error) {
    this.state = DOWNLOAD_STATES.FAILED;
    this.error = error;
    this.completedAt = new Date().toISOString();
  }

  /**
   * Mark download as cancelled
   */
  cancel() {
    this.state = DOWNLOAD_STATES.CANCELLED;
    this.completedAt = new Date().toISOString();
  }

  /**
   * Mark download as paused
   */
  pause() {
    this.state = DOWNLOAD_STATES.PAUSED;
  }

  /**
   * Mark download as interrupted
   */
  interrupt() {
    this.state = DOWNLOAD_STATES.INTERRUPTED;
  }

  /**
   * Resume download
   */
  resume() {
    if (this.state === DOWNLOAD_STATES.PAUSED || this.state === DOWNLOAD_STATES.INTERRUPTED) {
      this.state = DOWNLOAD_STATES.IN_PROGRESS;
    }
  }

  /**
   * Get download progress percentage
   * @returns {number} Progress percentage (0-100)
   */
  getProgress() {
    if (this.totalBytes === 0) return 0;
    return Math.round((this.receivedBytes / this.totalBytes) * 100);
  }

  /**
   * Get formatted file size
   * @returns {string} Formatted size string
   */
  getFormattedSize() {
    return formatFileSize(this.receivedBytes);
  }

  /**
   * Get formatted total size
   * @returns {string} Formatted total size string
   */
  getFormattedTotalSize() {
    return formatFileSize(this.totalBytes);
  }

  /**
   * Get download speed estimate
   * @returns {string} Speed estimate
   */
  getSpeed() {
    if (this.state !== DOWNLOAD_STATES.IN_PROGRESS) return '';
    
    const elapsedMs = new Date() - new Date(this.startedAt);
    const elapsedSeconds = elapsedMs / 1000;
    
    if (elapsedSeconds < 1) return '';
    
    const bytesPerSecond = this.receivedBytes / elapsedSeconds;
    return `${formatFileSize(bytesPerSecond)}/s`;
  }

  /**
   * Get estimated time remaining
   * @returns {string} Time remaining estimate
   */
  getTimeRemaining() {
    if (this.state !== DOWNLOAD_STATES.IN_PROGRESS || this.totalBytes === 0) return '';
    
    const remainingBytes = this.totalBytes - this.receivedBytes;
    const elapsedMs = new Date() - new Date(this.startedAt);
    const elapsedSeconds = elapsedMs / 1000;
    
    if (elapsedSeconds < 1 || remainingBytes <= 0) return '';
    
    const bytesPerSecond = this.receivedBytes / elapsedSeconds;
    const remainingSeconds = remainingBytes / bytesPerSecond;
    
    if (remainingSeconds < 60) return `${Math.round(remainingSeconds)}s`;
    if (remainingSeconds < 3600) return `${Math.round(remainingSeconds / 60)}m`;
    return `${Math.round(remainingSeconds / 3600)}h`;
  }

  /**
   * Check if download is active
   * @returns {boolean} True if download is active
   */
  isActive() {
    return [DOWNLOAD_STATES.PENDING, DOWNLOAD_STATES.IN_PROGRESS].includes(this.state);
  }

  /**
   * Check if download is finished
   * @returns {boolean} True if download is finished
   */
  isFinished() {
    return [
      DOWNLOAD_STATES.COMPLETED,
      DOWNLOAD_STATES.FAILED,
      DOWNLOAD_STATES.CANCELLED
    ].includes(this.state);
  }

  /**
   * Get download data for serialization
   * @returns {Object} Serializable data
   */
  toJSON() {
    return {
      id: this.id,
      filename: this.filename,
      url: this.url,
      savePath: this.savePath,
      totalBytes: this.totalBytes,
      receivedBytes: this.receivedBytes,
      state: this.state,
      startedAt: this.startedAt,
      completedAt: this.completedAt,
      error: this.error,
      mimeType: this.mimeType,
      websiteId: this.websiteId,
      tabId: this.tabId
    };
  }

  /**
   * Create Download instance from JSON data
   * @param {Object} data - JSON data
   * @returns {Download} Download instance
   */
  static fromJSON(data) {
    return new Download(data);
  }

  /**
   * Create download from Electron download item
   * @param {Object} item - Electron download item
   * @param {string} websiteId - Associated website ID
   * @param {string} tabId - Associated tab ID
   * @returns {Download} New download instance
   */
  static fromElectronItem(item, websiteId = null, tabId = null) {
    return new Download({
      filename: sanitizeFilename(item.getFilename()),
      url: item.getURL(),
      totalBytes: item.getTotalBytes(),
      mimeType: item.getMimeType(),
      websiteId,
      tabId
    });
  }
}
