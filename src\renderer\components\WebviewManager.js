/**
 * Webview management component for handling web content display
 */

import { generateId, formatUrl, logError, logInfo } from '../../shared/utils.js';

export class WebviewManager {
  constructor(dataService, eventService) {
    this.dataService = dataService;
    this.eventService = eventService;
    this.webviewContainer = document.getElementById('webviewContainer');
    this.loadingIndicator = document.getElementById('loadingIndicator');
    this.activeWebview = null;
    this.webviews = new Map(); // tabId -> webview element
    this.webviewSettings = {
      allowpopups: true,
      nodeintegration: false,
      webSecurity: false,
      allowRunningInsecureContent: true,
      experimentalFeatures: true
    };
    
    this.setupEventListeners();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    this.eventService.on('tab-switched', (tab) => {
      this.switchToWebview(tab.id);
    });

    this.eventService.on('tab-removed', (tab) => {
      this.removeWebview(tab.id);
    });

    this.eventService.on('navigate-to-url', (url) => {
      this.navigateActiveWebview(url);
    });

    this.eventService.on('webview-go-back', () => {
      this.goBack();
    });

    this.eventService.on('webview-go-forward', () => {
      this.goForward();
    });

    this.eventService.on('webview-reload', () => {
      this.reload();
    });

    this.eventService.on('webview-stop', () => {
      this.stop();
    });

    this.eventService.on('webview-zoom-in', () => {
      this.zoomIn();
    });

    this.eventService.on('webview-zoom-out', () => {
      this.zoomOut();
    });

    this.eventService.on('webview-reset-zoom', () => {
      this.resetZoom();
    });

    this.eventService.on('webview-toggle-devtools', () => {
      this.toggleDevTools();
    });

    this.eventService.on('webview-find-in-page', (query) => {
      this.findInPage(query);
    });

    this.eventService.on('webview-stop-find', () => {
      this.stopFindInPage();
    });
  }

  /**
   * Create webview for a tab
   * @param {Tab} tab - Tab to create webview for
   * @returns {HTMLElement} Created webview element
   */
  createWebview(tab) {
    if (this.webviews.has(tab.id)) {
      return this.webviews.get(tab.id);
    }

    const webview = document.createElement('webview');
    webview.id = `webview-${tab.id}`;
    webview.dataset.tabId = tab.id;
    webview.src = tab.url || 'about:blank';
    webview.style.cssText = `
      width: 100%;
      height: 100%;
      display: none;
      border: none;
      background: #ffffff;
    `;

    // Apply webview settings
    Object.keys(this.webviewSettings).forEach(key => {
      if (this.webviewSettings[key]) {
        webview.setAttribute(key, this.webviewSettings[key]);
      }
    });

    // Setup webview event listeners
    this.setupWebviewEventListeners(webview, tab);

    // Add to container and store reference
    this.webviewContainer.appendChild(webview);
    this.webviews.set(tab.id, webview);

    logInfo('WebviewManager', `Created webview for tab ${tab.id}`);
    return webview;
  }

  /**
   * Setup event listeners for a webview
   * @param {HTMLElement} webview - Webview element
   * @param {Tab} tab - Associated tab
   */
  setupWebviewEventListeners(webview, tab) {
    // Page loading events
    webview.addEventListener('dom-ready', () => {
      this.onWebviewDomReady(webview, tab);
    });

    webview.addEventListener('did-start-loading', () => {
      this.onWebviewStartLoading(webview, tab);
    });

    webview.addEventListener('did-stop-loading', () => {
      this.onWebviewStopLoading(webview, tab);
    });

    webview.addEventListener('did-fail-load', (event) => {
      this.onWebviewFailLoad(webview, tab, event);
    });

    // Navigation events
    webview.addEventListener('did-navigate', (event) => {
      this.onWebviewNavigate(webview, tab, event);
    });

    webview.addEventListener('did-navigate-in-page', (event) => {
      this.onWebviewNavigateInPage(webview, tab, event);
    });

    webview.addEventListener('will-navigate', (event) => {
      this.onWebviewWillNavigate(webview, tab, event);
    });

    // Page metadata events
    webview.addEventListener('page-title-updated', (event) => {
      this.onWebviewTitleUpdated(webview, tab, event);
    });

    webview.addEventListener('page-favicon-updated', (event) => {
      this.onWebviewFaviconUpdated(webview, tab, event);
    });

    // Window events
    webview.addEventListener('new-window', (event) => {
      this.onWebviewNewWindow(webview, tab, event);
    });

    // Context menu
    webview.addEventListener('context-menu', (event) => {
      this.onWebviewContextMenu(webview, tab, event);
    });

    // Console messages (for debugging)
    webview.addEventListener('console-message', (event) => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Webview Console] ${event.message}`);
      }
    });

    // Certificate error
    webview.addEventListener('certificate-error', (event) => {
      this.onWebviewCertificateError(webview, tab, event);
    });

    // Permission requests
    webview.addEventListener('permission-request', (event) => {
      this.onWebviewPermissionRequest(webview, tab, event);
    });

    // Media events
    webview.addEventListener('media-started-playing', () => {
      this.onWebviewMediaStarted(webview, tab);
    });

    webview.addEventListener('media-paused', () => {
      this.onWebviewMediaPaused(webview, tab);
    });
  }

  /**
   * Switch to webview for a specific tab
   * @param {string} tabId - Tab ID
   */
  switchToWebview(tabId) {
    // Hide all webviews
    this.webviews.forEach((webview, id) => {
      webview.style.display = id === tabId ? 'block' : 'none';
    });

    // Update active webview reference
    this.activeWebview = this.webviews.get(tabId) || null;

    // Update navigation state
    this.updateNavigationState();

    // Hide welcome screen if webview is active
    this.updateWelcomeScreenVisibility();

    logInfo('WebviewManager', `Switched to webview for tab ${tabId}`);
  }

  /**
   * Remove webview for a tab
   * @param {string} tabId - Tab ID
   */
  removeWebview(tabId) {
    const webview = this.webviews.get(tabId);
    if (webview) {
      // Remove event listeners
      webview.removeAllListeners?.();
      
      // Remove from DOM
      webview.remove();
      
      // Remove from map
      this.webviews.delete(tabId);

      // Clear active reference if this was active
      if (this.activeWebview === webview) {
        this.activeWebview = null;
      }

      logInfo('WebviewManager', `Removed webview for tab ${tabId}`);
    }
  }

  /**
   * Navigate active webview to URL
   * @param {string} url - URL to navigate to
   */
  navigateActiveWebview(url) {
    if (this.activeWebview && url) {
      const formattedUrl = formatUrl(url);
      this.activeWebview.src = formattedUrl;
      logInfo('WebviewManager', `Navigating to ${formattedUrl}`);
    }
  }

  /**
   * Go back in active webview
   */
  goBack() {
    if (this.activeWebview && this.canGoBack()) {
      this.activeWebview.goBack();
    }
  }

  /**
   * Go forward in active webview
   */
  goForward() {
    if (this.activeWebview && this.canGoForward()) {
      this.activeWebview.goForward();
    }
  }

  /**
   * Reload active webview
   */
  reload() {
    if (this.activeWebview) {
      this.activeWebview.reload();
    }
  }

  /**
   * Stop loading in active webview
   */
  stop() {
    if (this.activeWebview) {
      this.activeWebview.stop();
    }
  }

  /**
   * Check if active webview can go back
   * @returns {boolean} True if can go back
   */
  canGoBack() {
    return this.activeWebview ? this.activeWebview.canGoBack() : false;
  }

  /**
   * Check if active webview can go forward
   * @returns {boolean} True if can go forward
   */
  canGoForward() {
    return this.activeWebview ? this.activeWebview.canGoForward() : false;
  }

  /**
   * Get current URL of active webview
   * @returns {string} Current URL
   */
  getCurrentUrl() {
    return this.activeWebview ? this.activeWebview.getURL() : '';
  }

  /**
   * Get current title of active webview
   * @returns {string} Current title
   */
  getCurrentTitle() {
    return this.activeWebview ? this.activeWebview.getTitle() : '';
  }

  /**
   * Check if active webview is loading
   * @returns {boolean} True if loading
   */
  isLoading() {
    return this.activeWebview ? this.activeWebview.isLoading() : false;
  }

  // Zoom methods
  zoomIn() {
    if (this.activeWebview) {
      const currentZoom = this.activeWebview.getZoomFactor();
      this.activeWebview.setZoomFactor(Math.min(currentZoom + 0.1, 3.0));
      this.eventService.emit('zoom-changed', this.activeWebview.getZoomFactor());
    }
  }

  zoomOut() {
    if (this.activeWebview) {
      const currentZoom = this.activeWebview.getZoomFactor();
      this.activeWebview.setZoomFactor(Math.max(currentZoom - 0.1, 0.25));
      this.eventService.emit('zoom-changed', this.activeWebview.getZoomFactor());
    }
  }

  resetZoom() {
    if (this.activeWebview) {
      this.activeWebview.setZoomFactor(1.0);
      this.eventService.emit('zoom-changed', 1.0);
    }
  }

  getZoomFactor() {
    return this.activeWebview ? this.activeWebview.getZoomFactor() : 1.0;
  }

  // Developer tools
  toggleDevTools() {
    if (this.activeWebview) {
      if (this.activeWebview.isDevToolsOpened()) {
        this.activeWebview.closeDevTools();
      } else {
        this.activeWebview.openDevTools();
      }
    }
  }

  // Find in page
  findInPage(query, options = {}) {
    if (this.activeWebview && query) {
      const findOptions = {
        forward: true,
        findNext: false,
        matchCase: false,
        ...options
      };
      
      this.activeWebview.findInPage(query, findOptions);
    }
  }

  stopFindInPage() {
    if (this.activeWebview) {
      this.activeWebview.stopFindInPage('clearSelection');
    }
  }

  // Event handlers
  onWebviewDomReady(webview, tab) {
    this.updateTabFromWebview(tab, webview);
    this.eventService.emit('webview-dom-ready', { tab, webview });
  }

  onWebviewStartLoading(webview, tab) {
    tab.setLoading(true);
    this.showLoadingIndicator();
    this.eventService.emit('webview-start-loading', { tab, webview });
  }

  onWebviewStopLoading(webview, tab) {
    tab.setLoading(false);
    this.hideLoadingIndicator();
    this.updateTabFromWebview(tab, webview);
    this.eventService.emit('webview-stop-loading', { tab, webview });
  }

  onWebviewFailLoad(webview, tab, event) {
    tab.setLoading(false);
    this.hideLoadingIndicator();
    logError('WebviewManager', `Failed to load ${event.validatedURL}: ${event.errorDescription}`);
    this.eventService.emit('webview-fail-load', { tab, webview, event });
  }

  onWebviewNavigate(webview, tab, event) {
    tab.navigateTo(event.url);
    this.updateNavigationState();
    this.addToHistory(tab, event.url);
    this.eventService.emit('webview-navigate', { tab, webview, event });
  }

  onWebviewNavigateInPage(webview, tab, event) {
    tab.url = event.url;
    this.updateNavigationState();
    this.eventService.emit('webview-navigate-in-page', { tab, webview, event });
  }

  onWebviewWillNavigate(webview, tab, event) {
    this.eventService.emit('webview-will-navigate', { tab, webview, event });
  }

  onWebviewTitleUpdated(webview, tab, event) {
    tab.updateTitle(event.title);
    this.eventService.emit('webview-title-updated', { tab, webview, title: event.title });
  }

  onWebviewFaviconUpdated(webview, tab, event) {
    if (event.favicons && event.favicons.length > 0) {
      tab.updateFavicon(event.favicons[0]);
      this.eventService.emit('webview-favicon-updated', { tab, webview, favicon: event.favicons[0] });
    }
  }

  onWebviewNewWindow(webview, tab, event) {
    // Open new windows in new tabs
    this.eventService.emit('webview-new-window', { tab, webview, url: event.url });
  }

  onWebviewContextMenu(webview, tab, event) {
    this.eventService.emit('webview-context-menu', { tab, webview, event });
  }

  onWebviewCertificateError(webview, tab, event) {
    logError('WebviewManager', `Certificate error for ${event.url}: ${event.error}`);
    this.eventService.emit('webview-certificate-error', { tab, webview, event });
  }

  onWebviewPermissionRequest(webview, tab, event) {
    // Auto-grant common permissions for unrestricted browsing
    const allowedPermissions = ['media', 'geolocation', 'notifications', 'fullscreen'];
    if (allowedPermissions.includes(event.permission)) {
      event.request.allow();
    } else {
      event.request.deny();
    }
    this.eventService.emit('webview-permission-request', { tab, webview, event });
  }

  onWebviewMediaStarted(webview, tab) {
    this.eventService.emit('webview-media-started', { tab, webview });
  }

  onWebviewMediaPaused(webview, tab) {
    this.eventService.emit('webview-media-paused', { tab, webview });
  }

  // Helper methods
  updateTabFromWebview(tab, webview) {
    try {
      tab.canGoBack = webview.canGoBack();
      tab.canGoForward = webview.canGoForward();
      tab.updateNavigationState();
    } catch (error) {
      // Webview might not be ready
    }
  }

  updateNavigationState() {
    this.eventService.emit('navigation-state-changed', {
      canGoBack: this.canGoBack(),
      canGoForward: this.canGoForward(),
      url: this.getCurrentUrl(),
      title: this.getCurrentTitle(),
      isLoading: this.isLoading()
    });
  }

  addToHistory(tab, url) {
    if (url && url !== 'about:blank') {
      this.dataService.addHistoryEntry({
        url: url,
        title: tab.title,
        favicon: tab.favicon,
        websiteId: tab.websiteId,
        tabId: tab.id
      });
    }
  }

  showLoadingIndicator() {
    if (this.loadingIndicator) {
      this.loadingIndicator.style.display = 'flex';
    }
  }

  hideLoadingIndicator() {
    if (this.loadingIndicator) {
      this.loadingIndicator.style.display = 'none';
    }
  }

  updateWelcomeScreenVisibility() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
      welcomeScreen.style.display = this.webviews.size > 0 ? 'none' : 'flex';
    }
  }

  /**
   * Get active webview element
   * @returns {HTMLElement|null} Active webview or null
   */
  getActiveWebview() {
    return this.activeWebview;
  }

  /**
   * Execute JavaScript in active webview
   * @param {string} code - JavaScript code to execute
   * @returns {Promise} Promise that resolves with result
   */
  async executeJavaScript(code) {
    if (this.activeWebview) {
      return await this.activeWebview.executeJavaScript(code);
    }
    return null;
  }

  /**
   * Insert CSS into active webview
   * @param {string} css - CSS to insert
   */
  insertCSS(css) {
    if (this.activeWebview) {
      this.activeWebview.insertCSS(css);
    }
  }

  /**
   * Cleanup all webviews
   */
  cleanup() {
    this.webviews.forEach((webview, tabId) => {
      this.removeWebview(tabId);
    });
    this.webviews.clear();
    this.activeWebview = null;
  }
}
