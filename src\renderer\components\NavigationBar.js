/**
 * Navigation bar component for URL input and navigation controls
 */

import { formatUrl, isValidUrl, extractDomain } from '../../shared/utils.js';

export class NavigationBar {
  constructor(dataService, eventService, webviewManager) {
    this.dataService = dataService;
    this.eventService = eventService;
    this.webviewManager = webviewManager;
    
    // Get DOM elements
    this.backBtn = document.getElementById('backBtn');
    this.forwardBtn = document.getElementById('forwardBtn');
    this.refreshBtn = document.getElementById('refreshBtn');
    this.homeBtn = document.getElementById('homeBtn');
    this.urlInput = document.getElementById('urlInput');
    this.goBtn = document.getElementById('goBtn');
    this.securityIndicator = document.getElementById('securityIndicator');
    
    this.isNavigating = false;
    this.suggestions = [];
    
    this.setupEventListeners();
    this.setupUrlSuggestions();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Navigation buttons
    if (this.backBtn) {
      this.backBtn.addEventListener('click', () => {
        this.goBack();
      });
    }

    if (this.forwardBtn) {
      this.forwardBtn.addEventListener('click', () => {
        this.goForward();
      });
    }

    if (this.refreshBtn) {
      this.refreshBtn.addEventListener('click', () => {
        this.refresh();
      });
    }

    if (this.homeBtn) {
      this.homeBtn.addEventListener('click', () => {
        this.goHome();
      });
    }

    // URL input
    if (this.urlInput) {
      this.urlInput.addEventListener('keydown', (e) => {
        this.handleUrlInputKeydown(e);
      });

      this.urlInput.addEventListener('input', (e) => {
        this.handleUrlInputChange(e);
      });

      this.urlInput.addEventListener('focus', () => {
        this.handleUrlInputFocus();
      });

      this.urlInput.addEventListener('blur', () => {
        this.handleUrlInputBlur();
      });
    }

    // Go button
    if (this.goBtn) {
      this.goBtn.addEventListener('click', () => {
        this.navigate();
      });
    }

    // Event service listeners
    this.eventService.on('navigation-state-changed', (state) => {
      this.updateNavigationState(state);
    });

    this.eventService.on('tab-switched', (tab) => {
      this.updateFromTab(tab);
    });

    this.eventService.on('webview-navigate', ({ tab }) => {
      this.updateFromTab(tab);
    });

    this.eventService.on('webview-title-updated', ({ tab }) => {
      this.updateSecurityIndicator(tab.url);
    });
  }

  /**
   * Setup URL suggestions/autocomplete
   */
  setupUrlSuggestions() {
    if (!this.urlInput) return;

    // Create suggestions dropdown
    this.suggestionsDropdown = document.createElement('div');
    this.suggestionsDropdown.className = 'url-suggestions';
    this.suggestionsDropdown.style.cssText = `
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #ddd;
      border-top: none;
      border-radius: 0 0 4px 4px;
      max-height: 300px;
      overflow-y: auto;
      z-index: 1000;
      display: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    `;

    // Insert after URL input
    this.urlInput.parentNode.style.position = 'relative';
    this.urlInput.parentNode.appendChild(this.suggestionsDropdown);

    // Handle suggestion clicks
    this.suggestionsDropdown.addEventListener('click', (e) => {
      const suggestion = e.target.closest('.suggestion-item');
      if (suggestion) {
        this.selectSuggestion(suggestion.dataset.url);
      }
    });
  }

  /**
   * Handle URL input keydown events
   * @param {KeyboardEvent} event - Keydown event
   */
  handleUrlInputKeydown(event) {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        this.navigate();
        break;
      case 'Escape':
        this.urlInput.blur();
        this.hideSuggestions();
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.navigateSuggestions('down');
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.navigateSuggestions('up');
        break;
    }
  }

  /**
   * Handle URL input change events
   * @param {Event} event - Input event
   */
  handleUrlInputChange(event) {
    const query = event.target.value.trim();
    if (query.length > 0) {
      this.showSuggestions(query);
    } else {
      this.hideSuggestions();
    }
  }

  /**
   * Handle URL input focus
   */
  handleUrlInputFocus() {
    this.urlInput.select();
    if (this.urlInput.value.trim().length > 0) {
      this.showSuggestions(this.urlInput.value.trim());
    }
  }

  /**
   * Handle URL input blur
   */
  handleUrlInputBlur() {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      this.hideSuggestions();
    }, 150);
  }

  /**
   * Navigate to URL in input
   */
  navigate() {
    const input = this.urlInput.value.trim();
    if (!input) return;

    let url = input;

    // Check if it's a valid URL
    if (!isValidUrl(input)) {
      // Check if it looks like a domain
      if (input.includes('.') && !input.includes(' ')) {
        url = formatUrl(input);
      } else {
        // Treat as search query
        url = this.createSearchUrl(input);
      }
    }

    this.eventService.emit('navigate-to-url', url);
    this.urlInput.blur();
    this.hideSuggestions();
  }

  /**
   * Go back
   */
  goBack() {
    this.eventService.emit('webview-go-back');
  }

  /**
   * Go forward
   */
  goForward() {
    this.eventService.emit('webview-go-forward');
  }

  /**
   * Refresh current page
   */
  refresh() {
    this.eventService.emit('webview-reload');
  }

  /**
   * Go to home page
   */
  goHome() {
    const homepage = this.dataService.settings.homepage || 'about:blank';
    this.eventService.emit('navigate-to-url', homepage);
  }

  /**
   * Update navigation state
   * @param {Object} state - Navigation state
   */
  updateNavigationState(state) {
    if (this.backBtn) {
      this.backBtn.disabled = !state.canGoBack;
    }

    if (this.forwardBtn) {
      this.forwardBtn.disabled = !state.canGoForward;
    }

    if (this.urlInput && !this.isNavigating) {
      this.urlInput.value = state.url === 'about:blank' ? '' : state.url;
    }

    this.updateSecurityIndicator(state.url);
    this.updateLoadingState(state.isLoading);
  }

  /**
   * Update from tab data
   * @param {Tab} tab - Tab data
   */
  updateFromTab(tab) {
    if (this.urlInput && !this.isNavigating) {
      this.urlInput.value = tab.url === 'about:blank' ? '' : tab.url;
    }

    this.updateSecurityIndicator(tab.url);
    this.updateLoadingState(tab.isLoading);
  }

  /**
   * Update security indicator
   * @param {string} url - Current URL
   */
  updateSecurityIndicator(url) {
    if (!this.securityIndicator) return;

    const isSecure = url && url.startsWith('https://');
    const isLocal = url && (url.startsWith('file://') || url.startsWith('about:'));

    this.securityIndicator.innerHTML = '';

    if (isSecure) {
      this.securityIndicator.innerHTML = '<i class="fas fa-lock" title="Secure (HTTPS)"></i>';
      this.securityIndicator.className = 'security-indicator secure';
    } else if (isLocal) {
      this.securityIndicator.innerHTML = '<i class="fas fa-info-circle" title="Local"></i>';
      this.securityIndicator.className = 'security-indicator local';
    } else if (url && url.startsWith('http://')) {
      this.securityIndicator.innerHTML = '<i class="fas fa-exclamation-triangle" title="Not Secure (HTTP)"></i>';
      this.securityIndicator.className = 'security-indicator insecure';
    } else {
      this.securityIndicator.innerHTML = '<i class="fas fa-globe" title="Unknown"></i>';
      this.securityIndicator.className = 'security-indicator unknown';
    }
  }

  /**
   * Update loading state
   * @param {boolean} isLoading - Whether page is loading
   */
  updateLoadingState(isLoading) {
    if (this.refreshBtn) {
      const icon = this.refreshBtn.querySelector('i');
      if (icon) {
        if (isLoading) {
          icon.className = 'fas fa-times';
          this.refreshBtn.title = 'Stop';
          this.refreshBtn.onclick = () => this.eventService.emit('webview-stop');
        } else {
          icon.className = 'fas fa-sync-alt';
          this.refreshBtn.title = 'Refresh';
          this.refreshBtn.onclick = () => this.refresh();
        }
      }
    }
  }

  /**
   * Show URL suggestions
   * @param {string} query - Search query
   */
  showSuggestions(query) {
    const suggestions = this.generateSuggestions(query);
    
    if (suggestions.length === 0) {
      this.hideSuggestions();
      return;
    }

    this.suggestionsDropdown.innerHTML = '';
    
    suggestions.forEach((suggestion, index) => {
      const item = document.createElement('div');
      item.className = 'suggestion-item';
      item.dataset.url = suggestion.url;
      item.innerHTML = `
        <div class="suggestion-icon">
          <i class="${suggestion.icon}"></i>
        </div>
        <div class="suggestion-content">
          <div class="suggestion-title">${this.highlightQuery(suggestion.title, query)}</div>
          <div class="suggestion-url">${suggestion.url}</div>
        </div>
        <div class="suggestion-type">${suggestion.type}</div>
      `;
      
      this.suggestionsDropdown.appendChild(item);
    });

    this.suggestionsDropdown.style.display = 'block';
  }

  /**
   * Hide URL suggestions
   */
  hideSuggestions() {
    this.suggestionsDropdown.style.display = 'none';
  }

  /**
   * Generate suggestions based on query
   * @param {string} query - Search query
   * @returns {Array} Array of suggestions
   */
  generateSuggestions(query) {
    const suggestions = [];
    const lowerQuery = query.toLowerCase();

    // Website suggestions
    const matchingWebsites = this.dataService.websites
      .filter(website => website.matchesSearch(query))
      .slice(0, 5);

    matchingWebsites.forEach(website => {
      suggestions.push({
        title: website.name,
        url: website.url,
        icon: 'fas fa-bookmark',
        type: 'Website'
      });
    });

    // History suggestions
    const matchingHistory = this.dataService.history
      .filter(entry => entry.matchesSearch(query))
      .slice(0, 3);

    matchingHistory.forEach(entry => {
      suggestions.push({
        title: entry.getDisplayTitle(),
        url: entry.url,
        icon: 'fas fa-history',
        type: 'History'
      });
    });

    // Search suggestion
    if (query.length > 0) {
      suggestions.push({
        title: `Search for "${query}"`,
        url: this.createSearchUrl(query),
        icon: 'fas fa-search',
        type: 'Search'
      });
    }

    return suggestions.slice(0, 8); // Limit total suggestions
  }

  /**
   * Create search URL
   * @param {string} query - Search query
   * @returns {string} Search URL
   */
  createSearchUrl(query) {
    const searchEngine = this.dataService.settings.searchEngine || 'google';
    const searchUrls = {
      google: 'https://www.google.com/search?q=',
      bing: 'https://www.bing.com/search?q=',
      duckduckgo: 'https://duckduckgo.com/?q=',
      yahoo: 'https://search.yahoo.com/search?p='
    };

    const baseUrl = searchUrls[searchEngine] || searchUrls.google;
    return baseUrl + encodeURIComponent(query);
  }

  /**
   * Highlight query in text
   * @param {string} text - Text to highlight
   * @param {string} query - Query to highlight
   * @returns {string} Highlighted text
   */
  highlightQuery(text, query) {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  /**
   * Navigate suggestions with keyboard
   * @param {string} direction - 'up' or 'down'
   */
  navigateSuggestions(direction) {
    const items = this.suggestionsDropdown.querySelectorAll('.suggestion-item');
    if (items.length === 0) return;

    const currentSelected = this.suggestionsDropdown.querySelector('.suggestion-item.selected');
    let newIndex = 0;

    if (currentSelected) {
      const currentIndex = Array.from(items).indexOf(currentSelected);
      if (direction === 'down') {
        newIndex = (currentIndex + 1) % items.length;
      } else {
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
      }
      currentSelected.classList.remove('selected');
    }

    items[newIndex].classList.add('selected');
    items[newIndex].scrollIntoView({ block: 'nearest' });
    
    // Update URL input with selected suggestion
    this.urlInput.value = items[newIndex].dataset.url;
  }

  /**
   * Select a suggestion
   * @param {string} url - URL to select
   */
  selectSuggestion(url) {
    this.urlInput.value = url;
    this.navigate();
  }

  /**
   * Focus URL input
   */
  focus() {
    if (this.urlInput) {
      this.urlInput.focus();
    }
  }

  /**
   * Set URL input value
   * @param {string} url - URL to set
   */
  setUrl(url) {
    if (this.urlInput) {
      this.urlInput.value = url;
    }
  }
}
