/**
 * Tab management component
 */

import { Tab } from '../models/Tab.js';
import { generateId, escapeHtml } from '../../shared/utils.js';

export class TabManager {
  constructor(dataService, eventService, webviewManager) {
    this.dataService = dataService;
    this.eventService = eventService;
    this.webviewManager = webviewManager;
    this.tabsContainer = document.getElementById('tabsContainer');
    this.newTabBtn = document.getElementById('newTabBtn');
    this.activeTabId = null;
    this.maxTabs = 20;

    this.setupEventListeners();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // New tab button
    this.newTabBtn.addEventListener('click', () => {
      this.createNewTab();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 't':
            e.preventDefault();
            this.createNewTab();
            break;
          case 'w':
            e.preventDefault();
            this.closeActiveTab();
            break;
          case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
              this.switchToPreviousTab();
            } else {
              this.switchToNextTab();
            }
            break;
        }
      }
    });

    // Event service listeners
    this.eventService.on('new-tab', () => this.createNewTab());
    this.eventService.on('close-tab', () => this.closeActiveTab());
    this.eventService.on('next-tab', () => this.switchToNextTab());
    this.eventService.on('prev-tab', () => this.switchToPreviousTab());
  }

  /**
   * Create a new tab
   * @param {string} url - Initial URL (optional)
   * @param {string} websiteId - Associated website ID (optional)
   * @returns {Tab} Created tab
   */
  createNewTab(url = null, websiteId = null) {
    if (this.dataService.tabs.length >= this.maxTabs) {
      this.eventService.emit('max-tabs-reached');
      return null;
    }

    const tabData = {
      url: url || 'about:blank',
      websiteId: websiteId
    };

    const tab = this.dataService.addTab(tabData);
    this.renderTab(tab);
    this.webviewManager.createWebview(tab);
    this.switchToTab(tab.id);

    return tab;
  }

  /**
   * Close a tab
   * @param {string} tabId - Tab ID
   */
  closeTab(tabId) {
    const tab = this.dataService.getTabById(tabId);
    if (!tab) return;

    // Remove tab element
    const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
    if (tabElement) {
      tabElement.remove();
    }

    // Remove webview
    this.webviewManager.removeWebview(tabId);

    // Remove from data service
    this.dataService.removeTab(tabId);

    // Switch to another tab if this was active
    if (this.activeTabId === tabId) {
      const remainingTabs = this.dataService.tabs;
      if (remainingTabs.length > 0) {
        this.switchToTab(remainingTabs[0].id);
      } else {
        this.activeTabId = null;
        this.showWelcomeScreen();
      }
    }
  }

  /**
   * Close the active tab
   */
  closeActiveTab() {
    if (this.activeTabId) {
      this.closeTab(this.activeTabId);
    }
  }

  /**
   * Switch to a tab
   * @param {string} tabId - Tab ID
   */
  switchToTab(tabId) {
    const tab = this.dataService.getTabById(tabId);
    if (!tab) return;

    // Update active state in data
    this.dataService.setActiveTab(tabId);
    this.activeTabId = tabId;

    // Update UI
    this.updateTabVisuals();
    this.updateWebviewVisibility();
    this.updateNavigationState();

    this.eventService.emit('tab-switched', tab);
  }

  /**
   * Switch to next tab
   */
  switchToNextTab() {
    const tabs = this.dataService.tabs;
    if (tabs.length <= 1) return;

    const currentIndex = tabs.findIndex(t => t.id === this.activeTabId);
    const nextIndex = (currentIndex + 1) % tabs.length;
    this.switchToTab(tabs[nextIndex].id);
  }

  /**
   * Switch to previous tab
   */
  switchToPreviousTab() {
    const tabs = this.dataService.tabs;
    if (tabs.length <= 1) return;

    const currentIndex = tabs.findIndex(t => t.id === this.activeTabId);
    const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
    this.switchToTab(tabs[prevIndex].id);
  }

  /**
   * Render a tab in the UI
   * @param {Tab} tab - Tab to render
   */
  renderTab(tab) {
    const tabElement = document.createElement('div');
    tabElement.className = 'tab';
    tabElement.dataset.tabId = tab.id;
    
    tabElement.innerHTML = `
      <div class="tab-favicon">
        <i class="fas fa-globe"></i>
      </div>
      <div class="tab-title">${escapeHtml(tab.getDisplayTitle())}</div>
      <div class="tab-close" title="Close tab">
        <i class="fas fa-times"></i>
      </div>
    `;

    // Add event listeners
    tabElement.addEventListener('click', (e) => {
      if (!e.target.closest('.tab-close')) {
        this.switchToTab(tab.id);
      }
    });

    tabElement.querySelector('.tab-close').addEventListener('click', (e) => {
      e.stopPropagation();
      this.closeTab(tab.id);
    });

    // Add context menu
    tabElement.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.showTabContextMenu(e, tab);
    });

    this.tabsContainer.appendChild(tabElement);
  }



  /**
   * Update tab visuals
   */
  updateTabVisuals() {
    document.querySelectorAll('.tab').forEach(tabElement => {
      const tabId = tabElement.dataset.tabId;
      tabElement.classList.toggle('active', tabId === this.activeTabId);
    });
  }

  /**
   * Update webview visibility
   */
  updateWebviewVisibility() {
    // Delegate to webview manager
    this.webviewManager.switchToWebview(this.activeTabId);
  }

  /**
   * Update tab title
   * @param {Tab} tab - Tab to update
   */
  updateTabTitle(tab) {
    const tabElement = document.querySelector(`[data-tab-id="${tab.id}"]`);
    if (tabElement) {
      const titleElement = tabElement.querySelector('.tab-title');
      titleElement.textContent = tab.getDisplayTitle();
    }
  }

  /**
   * Update tab favicon
   * @param {Tab} tab - Tab to update
   */
  updateTabFavicon(tab) {
    const tabElement = document.querySelector(`[data-tab-id="${tab.id}"]`);
    if (tabElement) {
      const faviconElement = tabElement.querySelector('.tab-favicon');
      if (tab.favicon) {
        faviconElement.innerHTML = `<img src="${tab.favicon}" alt="favicon" onerror="this.style.display='none'">`;
      } else {
        faviconElement.innerHTML = '<i class="fas fa-globe"></i>';
      }
    }
  }

  /**
   * Update tab loading state
   * @param {Tab} tab - Tab to update
   */
  updateTabLoadingState(tab) {
    const tabElement = document.querySelector(`[data-tab-id="${tab.id}"]`);
    if (tabElement) {
      tabElement.classList.toggle('loading', tab.isLoading);
    }
  }

  /**
   * Update navigation state
   */
  updateNavigationState() {
    const activeTab = this.dataService.getActiveTab();
    if (!activeTab) return;

    const backBtn = document.getElementById('backBtn');
    const forwardBtn = document.getElementById('forwardBtn');
    const urlInput = document.getElementById('urlInput');

    if (backBtn) backBtn.disabled = !activeTab.canGoBack;
    if (forwardBtn) forwardBtn.disabled = !activeTab.canGoForward;
    if (urlInput) urlInput.value = activeTab.url === 'about:blank' ? '' : activeTab.url;
  }

  /**
   * Add to history
   * @param {Tab} tab - Tab
   * @param {string} url - URL
   */
  addToHistory(tab, url) {
    if (url && url !== 'about:blank') {
      this.dataService.addHistoryEntry({
        url: url,
        title: tab.title,
        favicon: tab.favicon,
        websiteId: tab.websiteId,
        tabId: tab.id
      });
    }
  }

  /**
   * Show tab context menu
   * @param {Event} event - Context menu event
   * @param {Tab} tab - Tab
   */
  showTabContextMenu(event, tab) {
    // Implementation would show context menu
    this.eventService.emit('show-tab-context-menu', { event, tab });
  }

  /**
   * Show welcome screen
   */
  showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
      welcomeScreen.style.display = 'flex';
    }
  }

  /**
   * Navigate active tab to URL
   * @param {string} url - URL to navigate to
   */
  navigateActiveTab(url) {
    const activeTab = this.dataService.getActiveTab();
    if (activeTab) {
      const webview = document.querySelector(`webview[data-tab-id="${activeTab.id}"]`);
      if (webview) {
        webview.src = url;
      }
    }
  }

  /**
   * Reload active tab
   */
  reloadActiveTab() {
    const activeTab = this.dataService.getActiveTab();
    if (activeTab) {
      const webview = document.querySelector(`webview[data-tab-id="${activeTab.id}"]`);
      if (webview) {
        webview.reload();
      }
    }
  }

  /**
   * Go back in active tab
   */
  goBackActiveTab() {
    const activeTab = this.dataService.getActiveTab();
    if (activeTab && activeTab.canGoBack) {
      const webview = document.querySelector(`webview[data-tab-id="${activeTab.id}"]`);
      if (webview) {
        webview.goBack();
      }
    }
  }

  /**
   * Go forward in active tab
   */
  goForwardActiveTab() {
    const activeTab = this.dataService.getActiveTab();
    if (activeTab && activeTab.canGoForward) {
      const webview = document.querySelector(`webview[data-tab-id="${activeTab.id}"]`);
      if (webview) {
        webview.goForward();
      }
    }
  }

  /**
   * Get active webview
   * @returns {HTMLElement|null} Active webview element
   */
  getActiveWebview() {
    if (this.activeTabId) {
      return document.querySelector(`webview[data-tab-id="${this.activeTabId}"]`);
    }
    return null;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // Remove all tabs and webviews
    this.dataService.tabs.forEach(tab => {
      this.closeTab(tab.id);
    });
  }
}
