/**
 * Download management module
 */

const { session } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

class DownloadManager {
  constructor(windowManager) {
    this.windowManager = windowManager;
    this.activeDownloads = new Map();
    this.setupDownloadHandling();
  }

  /**
   * Setup download handling for the default session
   */
  setupDownloadHandling() {
    session.defaultSession.on('will-download', (event, item, webContents) => {
      this.handleDownload(item, webContents);
    });
  }

  /**
   * Handle a download item
   * @param {DownloadItem} item - Electron download item
   * @param {WebContents} webContents - Web contents that initiated the download
   */
  handleDownload(item, webContents) {
    try {
      const downloadId = this.generateDownloadId();
      const downloadsPath = this.getDownloadsPath();
      
      // Ensure downloads directory exists
      this.ensureDownloadsDirectory(downloadsPath);
      
      // Set the save path
      const filename = this.sanitizeFilename(item.getFilename());
      const filePath = path.join(downloadsPath, filename);
      item.setSavePath(filePath);

      // Store download info
      const downloadInfo = {
        id: downloadId,
        filename: filename,
        url: item.getURL(),
        totalBytes: item.getTotalBytes(),
        receivedBytes: 0,
        state: 'started',
        startTime: Date.now(),
        savePath: filePath,
        mimeType: item.getMimeType()
      };

      this.activeDownloads.set(downloadId, downloadInfo);

      // Send download started event
      this.sendDownloadEvent('download-started', downloadInfo);

      // Setup download event handlers
      this.setupDownloadItemHandlers(item, downloadId, downloadInfo);

    } catch (error) {
      console.error('Error handling download:', error);
    }
  }

  /**
   * Setup event handlers for a download item
   * @param {DownloadItem} item - Download item
   * @param {string} downloadId - Download ID
   * @param {Object} downloadInfo - Download information
   */
  setupDownloadItemHandlers(item, downloadId, downloadInfo) {
    // Handle download progress
    item.on('updated', (event, state) => {
      if (!this.activeDownloads.has(downloadId)) return;

      const info = this.activeDownloads.get(downloadId);
      
      if (state === 'interrupted') {
        info.state = 'interrupted';
        this.sendDownloadEvent('download-interrupted', info);
      } else if (state === 'progressing') {
        if (item.isPaused()) {
          info.state = 'paused';
          this.sendDownloadEvent('download-paused', info);
        } else {
          const totalBytes = item.getTotalBytes();
          const receivedBytes = item.getReceivedBytes();
          
          info.totalBytes = totalBytes;
          info.receivedBytes = receivedBytes;
          info.state = 'progressing';
          info.progress = totalBytes > 0 ? Math.round((receivedBytes / totalBytes) * 100) : 0;
          info.speed = this.calculateDownloadSpeed(info);
          info.timeRemaining = this.calculateTimeRemaining(info);
          
          this.activeDownloads.set(downloadId, info);
          this.sendDownloadEvent('download-progress', info);
        }
      }
    });

    // Handle download completion
    item.once('done', (event, state) => {
      if (!this.activeDownloads.has(downloadId)) return;

      const info = this.activeDownloads.get(downloadId);
      info.endTime = Date.now();
      
      if (state === 'completed') {
        info.state = 'completed';
        info.receivedBytes = info.totalBytes;
        info.progress = 100;
        info.savePath = item.getSavePath();
        this.sendDownloadEvent('download-completed', info);
      } else {
        info.state = 'failed';
        info.error = state;
        this.sendDownloadEvent('download-failed', info);
      }

      // Keep completed downloads for history, but remove active tracking
      setTimeout(() => {
        this.activeDownloads.delete(downloadId);
      }, 5000);
    });
  }

  /**
   * Calculate download speed
   * @param {Object} downloadInfo - Download information
   * @returns {number} Speed in bytes per second
   */
  calculateDownloadSpeed(downloadInfo) {
    const elapsedMs = Date.now() - downloadInfo.startTime;
    const elapsedSeconds = elapsedMs / 1000;
    
    if (elapsedSeconds < 1) return 0;
    
    return Math.round(downloadInfo.receivedBytes / elapsedSeconds);
  }

  /**
   * Calculate estimated time remaining
   * @param {Object} downloadInfo - Download information
   * @returns {number} Time remaining in seconds
   */
  calculateTimeRemaining(downloadInfo) {
    if (downloadInfo.totalBytes === 0 || downloadInfo.speed === 0) return 0;
    
    const remainingBytes = downloadInfo.totalBytes - downloadInfo.receivedBytes;
    return Math.round(remainingBytes / downloadInfo.speed);
  }

  /**
   * Send download event to renderer
   * @param {string} event - Event name
   * @param {Object} data - Event data
   */
  sendDownloadEvent(event, data) {
    if (this.windowManager.isMainWindowAvailable()) {
      this.windowManager.sendToMainWindow(event, data);
    }
  }

  /**
   * Get downloads directory path
   * @returns {string} Downloads path
   */
  getDownloadsPath() {
    return path.join(os.homedir(), 'Downloads');
  }

  /**
   * Ensure downloads directory exists
   * @param {string} downloadsPath - Downloads directory path
   */
  ensureDownloadsDirectory(downloadsPath) {
    if (!fs.existsSync(downloadsPath)) {
      fs.mkdirSync(downloadsPath, { recursive: true });
    }
  }

  /**
   * Sanitize filename for file system
   * @param {string} filename - Original filename
   * @returns {string} Sanitized filename
   */
  sanitizeFilename(filename) {
    return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
  }

  /**
   * Generate unique download ID
   * @returns {string} Unique ID
   */
  generateDownloadId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Get active downloads
   * @returns {Array} Array of active downloads
   */
  getActiveDownloads() {
    return Array.from(this.activeDownloads.values());
  }

  /**
   * Cancel a download
   * @param {string} downloadId - Download ID
   */
  cancelDownload(downloadId) {
    if (this.activeDownloads.has(downloadId)) {
      const info = this.activeDownloads.get(downloadId);
      info.state = 'cancelled';
      this.sendDownloadEvent('download-cancelled', info);
      this.activeDownloads.delete(downloadId);
    }
  }

  /**
   * Pause a download
   * @param {string} downloadId - Download ID
   */
  pauseDownload(downloadId) {
    // Note: Electron doesn't provide direct pause/resume functionality
    // This would need to be implemented at the webview level
    if (this.activeDownloads.has(downloadId)) {
      const info = this.activeDownloads.get(downloadId);
      info.state = 'paused';
      this.sendDownloadEvent('download-paused', info);
    }
  }

  /**
   * Resume a download
   * @param {string} downloadId - Download ID
   */
  resumeDownload(downloadId) {
    if (this.activeDownloads.has(downloadId)) {
      const info = this.activeDownloads.get(downloadId);
      info.state = 'progressing';
      this.sendDownloadEvent('download-resumed', info);
    }
  }

  /**
   * Clear completed downloads
   */
  clearCompletedDownloads() {
    const activeIds = [];
    this.activeDownloads.forEach((info, id) => {
      if (info.state === 'completed' || info.state === 'failed' || info.state === 'cancelled') {
        activeIds.push(id);
      }
    });

    activeIds.forEach(id => {
      this.activeDownloads.delete(id);
    });

    this.sendDownloadEvent('downloads-cleared', { count: activeIds.length });
  }

  /**
   * Get download statistics
   * @returns {Object} Download statistics
   */
  getDownloadStats() {
    const downloads = Array.from(this.activeDownloads.values());
    
    return {
      total: downloads.length,
      active: downloads.filter(d => d.state === 'progressing').length,
      completed: downloads.filter(d => d.state === 'completed').length,
      failed: downloads.filter(d => d.state === 'failed').length,
      paused: downloads.filter(d => d.state === 'paused').length
    };
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.activeDownloads.clear();
    session.defaultSession.removeAllListeners('will-download');
  }
}

module.exports = DownloadManager;
