/**
 * Application constants and configuration
 */

// Application metadata
export const APP_CONFIG = {
  name: 'Unrestricted Browser',
  version: '1.0.0',
  description: 'A desktop application for unrestricted website browsing'
};

// Default window configuration
export const WINDOW_CONFIG = {
  width: 1400,
  height: 900,
  minWidth: 1000,
  minHeight: 600,
  backgroundColor: '#1a1a1a'
};

// Website categories
export const WEBSITE_CATEGORIES = [
  'general',
  'social',
  'news',
  'entertainment',
  'work',
  'education',
  'shopping',
  'other'
];

// Default settings
export const DEFAULT_SETTINGS = {
  autoLoadLastWebsite: false,
  showNotifications: true,
  clearDataOnExit: false,
  blockAds: false,
  homepage: 'about:blank'
};

// IPC channel names
export const IPC_CHANNELS = {
  // Store operations
  STORE_GET: 'store-get',
  STORE_SET: 'store-set',
  STORE_DELETE: 'store-delete',
  STORE_CLEAR: 'store-clear',
  
  // App operations
  GET_APP_VERSION: 'get-app-version',
  SHOW_MESSAGE_BOX: 'show-message-box',
  SHOW_SAVE_DIALOG: 'show-save-dialog',
  SHOW_OPEN_DIALOG: 'show-open-dialog',
  WRITE_FILE: 'write-file',
  READ_FILE: 'read-file',
  
  // Downloads
  OPEN_DOWNLOADS_FOLDER: 'open-downloads-folder',
  GET_DOWNLOADS_PATH: 'get-downloads-path',
  
  // Events
  APP_READY: 'app-ready',
  SHOW_ADD_WEBSITE_DIALOG: 'show-add-website-dialog',
  IMPORT_WEBSITES: 'import-websites',
  EXPORT_WEBSITES: 'export-websites',
  RELOAD_CURRENT_SITE: 'reload-current-site',
  ZOOM_IN: 'zoom-in',
  ZOOM_OUT: 'zoom-out',
  RESET_ZOOM: 'reset-zoom',
  CLEAR_ALL_DATA: 'clear-all-data',
  SHOW_SETTINGS: 'show-settings',
  SHOW_HISTORY: 'show-history',
  SHOW_DOWNLOADS: 'show-downloads',
  SHOW_FIND: 'show-find',
  TOGGLE_DEV_TOOLS: 'toggle-dev-tools',
  NEW_TAB: 'new-tab',
  CLOSE_TAB: 'close-tab',
  NEXT_TAB: 'next-tab',
  PREV_TAB: 'prev-tab',
  
  // Download events
  DOWNLOAD_STARTED: 'download-started',
  DOWNLOAD_PROGRESS: 'download-progress',
  DOWNLOAD_COMPLETED: 'download-completed',
  DOWNLOAD_FAILED: 'download-failed',
  DOWNLOAD_INTERRUPTED: 'download-interrupted',
  DOWNLOAD_PAUSED: 'download-paused'
};

// Zoom levels
export const ZOOM_LEVELS = [0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 0.9, 1.0, 1.1, 1.25, 1.5, 1.75, 2.0, 2.5, 3.0, 4.0, 5.0];

// File extensions for import/export
export const FILE_FILTERS = {
  JSON: { name: 'JSON Files', extensions: ['json'] },
  ALL: { name: 'All Files', extensions: ['*'] }
};

// Error messages
export const ERROR_MESSAGES = {
  WEBSITE_NOT_FOUND: 'Website not found',
  TAB_NOT_FOUND: 'Tab not found',
  INVALID_URL: 'Invalid URL provided',
  STORAGE_ERROR: 'Error accessing storage',
  FILE_READ_ERROR: 'Error reading file',
  FILE_WRITE_ERROR: 'Error writing file',
  NETWORK_ERROR: 'Network error occurred'
};

// Success messages
export const SUCCESS_MESSAGES = {
  WEBSITE_ADDED: 'Website added successfully',
  WEBSITE_UPDATED: 'Website updated successfully',
  WEBSITE_DELETED: 'Website deleted successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
  DATA_EXPORTED: 'Data exported successfully',
  DATA_IMPORTED: 'Data imported successfully',
  DATA_CLEARED: 'All data cleared successfully'
};

// Storage keys
export const STORAGE_KEYS = {
  WEBSITES: 'websites',
  SETTINGS: 'settings',
  HISTORY: 'history',
  DOWNLOADS: 'downloads',
  LAST_WEBSITE: 'lastWebsite',
  WINDOW_STATE: 'windowState'
};
