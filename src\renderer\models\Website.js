/**
 * Website model class
 */

import { generateId, isValidUrl, formatUrl, extractDomain, getFaviconUrl } from '../../shared/utils.js';
import { WEBSITE_CATEGORIES, ERROR_MESSAGES } from '../../shared/constants.js';

export class Website {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.name = data.name || '';
    this.url = data.url || '';
    this.category = data.category || 'general';
    this.isFavorite = data.isFavorite || false;
    this.visitCount = data.visitCount || 0;
    this.lastVisited = data.lastVisited || null;
    this.dateAdded = data.dateAdded || new Date().toISOString();
    this.favicon = data.favicon || null;
    this.description = data.description || '';
    this.tags = data.tags || [];
    
    this.validate();
  }

  /**
   * Validate website data
   * @throws {Error} If validation fails
   */
  validate() {
    if (!this.name || this.name.trim().length === 0) {
      throw new Error('Website name is required');
    }

    if (!this.url || this.url.trim().length === 0) {
      throw new Error('Website URL is required');
    }

    if (!isValidUrl(formatUrl(this.url))) {
      throw new Error(ERROR_MESSAGES.INVALID_URL);
    }

    if (!WEBSITE_CATEGORIES.includes(this.category)) {
      throw new Error('Invalid website category');
    }

    // Normalize URL
    this.url = formatUrl(this.url);
    
    // Set favicon if not provided
    if (!this.favicon) {
      this.favicon = getFaviconUrl(this.url);
    }
  }

  /**
   * Update website data
   * @param {Object} data - Data to update
   */
  update(data) {
    const allowedFields = ['name', 'url', 'category', 'isFavorite', 'description', 'tags'];
    
    allowedFields.forEach(field => {
      if (data.hasOwnProperty(field)) {
        this[field] = data[field];
      }
    });

    this.validate();
  }

  /**
   * Record a visit to this website
   */
  recordVisit() {
    this.visitCount++;
    this.lastVisited = new Date().toISOString();
  }

  /**
   * Toggle favorite status
   */
  toggleFavorite() {
    this.isFavorite = !this.isFavorite;
  }

  /**
   * Get domain name
   * @returns {string} Domain name
   */
  getDomain() {
    return extractDomain(this.url);
  }

  /**
   * Check if website matches search query
   * @param {string} query - Search query
   * @returns {boolean} True if matches
   */
  matchesSearch(query) {
    if (!query || query.trim().length === 0) return true;
    
    const searchTerm = query.toLowerCase().trim();
    const searchableText = [
      this.name,
      this.url,
      this.category,
      this.description,
      ...this.tags
    ].join(' ').toLowerCase();
    
    return searchableText.includes(searchTerm);
  }

  /**
   * Get website data for serialization
   * @returns {Object} Serializable data
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      url: this.url,
      category: this.category,
      isFavorite: this.isFavorite,
      visitCount: this.visitCount,
      lastVisited: this.lastVisited,
      dateAdded: this.dateAdded,
      favicon: this.favicon,
      description: this.description,
      tags: this.tags
    };
  }

  /**
   * Create Website instance from JSON data
   * @param {Object} data - JSON data
   * @returns {Website} Website instance
   */
  static fromJSON(data) {
    return new Website(data);
  }

  /**
   * Create Website instance from URL
   * @param {string} url - Website URL
   * @param {string} name - Optional name (defaults to domain)
   * @returns {Website} Website instance
   */
  static fromUrl(url, name = null) {
    const formattedUrl = formatUrl(url);
    const websiteName = name || extractDomain(formattedUrl);
    
    return new Website({
      name: websiteName,
      url: formattedUrl
    });
  }

  /**
   * Validate website data without creating instance
   * @param {Object} data - Data to validate
   * @returns {Object} Validation result
   */
  static validate(data) {
    const errors = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Website name is required');
    }

    if (!data.url || data.url.trim().length === 0) {
      errors.push('Website URL is required');
    } else if (!isValidUrl(formatUrl(data.url))) {
      errors.push(ERROR_MESSAGES.INVALID_URL);
    }

    if (data.category && !WEBSITE_CATEGORIES.includes(data.category)) {
      errors.push('Invalid website category');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
